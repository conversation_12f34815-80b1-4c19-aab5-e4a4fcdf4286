import React, { useState, useEffect } from 'react';
import { subscribeToChangelog, CATEGORY_INFO } from '../services/changelog';
import { getFormattedVersion } from '../utils/version';
import './Changelog.css';

// Default changelog entries for immediate display
const getDefaultChangelogEntries = () => [
  {
    id: 'v1.2.1',
    version: '1.2.1',
    title: 'Mobile Profile Menu Improvements - Better Mobile Experience! 📱',
    description: 'We\'ve significantly improved the mobile profile dropdown menu to make it more user-friendly and accessible. The menu now displays properly on all mobile devices with better spacing and positioning.',
    category: 'improvement',
    releaseDate: new Date().toISOString(),
    changes: [
      'Fixed mobile profile dropdown menu positioning to prevent cutoff',
      'Improved dropdown menu width and spacing on mobile devices',
      'Enhanced touch-friendly interaction areas for better mobile usability',
      'Removed unwanted pill-shaped borders that were causing display issues',
      'Better centered content alignment within the profile dropdown',
      'Optimized dropdown positioning for different screen sizes',
      'Improved visual consistency across mobile and desktop views',
      'Enhanced accessibility with proper touch targets and spacing'
    ],
    isBreaking: false,
    timestamp: Date.now()
  },
  {
    id: 'v1.2.0',
    version: '1.2.0',
    title: 'Major Platform Improvements - Better Performance & Mobile Experience! ⚡',
    description: 'We\'ve made significant improvements to make NAROOP faster, more reliable, and easier to use on all devices. This update focuses on better performance, improved mobile experience, and fixing issues that were affecting your daily use.',
    category: 'improvement',
    releaseDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    changes: [
      'Much faster page loading and smoother interactions throughout the platform',
      'Improved mobile experience with better touch targets and navigation',
      'Fixed issues where buttons and links were hard to tap on mobile devices',
      'Better text readability with improved font sizes and spacing',
      'Smoother animations and transitions when navigating between pages',
      'Fixed story sharing issues - your stories now save more reliably',
      'Improved error messages that are easier to understand',
      'Better performance when viewing long lists of stories or content',
      'Enhanced accessibility for users with disabilities',
      'Fixed layout issues that caused content to appear cut off on some screens',
      'Improved consistency in colors, spacing, and button styles across the platform',
      'Better memory usage - the app now uses less of your device\'s resources'
    ],
    isBreaking: false,
    timestamp: Date.now()
  },
  {
    id: 'v1.1.1',
    version: '1.1.1',
    title: 'Critical Bug Fixes - Story Submission & Mobile Navigation 🔧',
    description: 'We\'ve fixed several important issues that were affecting your experience on NAROOP. Story sharing now works smoothly, and mobile navigation is much improved!',
    category: 'bugfix',
    releaseDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    changes: [
      'Fixed story submission errors - you can now share your stories without permission issues',
      'Improved mobile menu - all navigation options are now visible and scrollable on mobile devices',
      'Enhanced form validation with clearer error messages',
      'Better authentication handling to prevent login-related issues',
      'Improved mobile touch targets for easier navigation on phones and tablets',
      'Fixed duplicate Kids Zone display issue for cleaner navigation',
      'Added better error handling throughout the platform',
      'Enhanced security with updated Firebase rules'
    ],
    isBreaking: false,
    timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000
  },
  {
    id: 'v1.1.0',
    version: '1.1.0',
    title: 'Fresh New Look - Green Theme Update! 🌱',
    description: 'We\'ve given NAROOP a beautiful new look! The login page and overall design now features a fresh green color scheme that represents growth, prosperity, and positive energy.',
    category: 'improvement',
    releaseDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    changes: [
      'Updated login page with a beautiful green color theme',
      'Replaced orange and purple colors with calming green tones',
      'New green background gradients throughout the platform',
      'Improved visual consistency across all pages',
      'Enhanced user experience with nature-inspired colors',
      'Better color accessibility and readability'
    ],
    isBreaking: false,
    timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000
  },
  {
    id: 'v1.0.0',
    version: '1.0.0',
    title: 'Welcome to NAROOP! 🎉',
    description: 'The official launch of NAROOP - Narrative of Our People. A platform dedicated to sharing positive stories and building community within the Black community.',
    category: 'feature',
    releaseDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    changes: [
      'Story sharing platform with rich text editor',
      'Community features for connecting with others',
      'Kids Zone with age-appropriate content',
      'User authentication and profiles',
      'Mobile-responsive design',
      'Task management and goal setting',
      'Messaging system for community interaction',
      'Newsfeed for discovering new stories'
    ],
    isBreaking: false,
    timestamp: Date.now() - 7 * 24 * 60 * 60 * 1000
  }
];

const Changelog = ({ onClose }) => {
  const [changelogEntries, setChangelogEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    let unsubscribe;

    const setupChangelog = async () => {
      try {
        // Try to load from Firebase first
        unsubscribe = subscribeToChangelog((entries) => {
          console.log('Changelog entries received:', entries);
          if (entries && entries.length > 0) {
            setChangelogEntries(entries);
            setLoading(false);
            setError(null);
          } else {
            // If no entries from Firebase, use default entries
            console.log('No Firebase entries, using default changelog');
            setChangelogEntries(getDefaultChangelogEntries());
            setLoading(false);
            setError(null);
          }
        });
      } catch (err) {
        console.error('Error setting up changelog subscription:', err);
        // Fallback to default entries if Firebase fails
        console.log('Firebase failed, using default changelog');
        setChangelogEntries(getDefaultChangelogEntries());
        setLoading(false);
        setError(null);
      }
    };

    // Add a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.log('Changelog loading timeout - using default entries');
        setChangelogEntries(getDefaultChangelogEntries());
        setLoading(false);
        setError(null);
      }
    }, 5000); // 5 second timeout

    setupChangelog();

    return () => {
      clearTimeout(timeoutId);
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [loading]);

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Unknown date';
    }
  };

  const filteredEntries = selectedCategory === 'all' 
    ? changelogEntries 
    : changelogEntries.filter(entry => entry.category === selectedCategory);

  const categories = Object.keys(CATEGORY_INFO);

  if (loading) {
    return (
      <div className="changelog-overlay" onClick={onClose}>
        <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
          <div className="changelog-header">
            <div className="changelog-title">
              <h2>📋 Changelog</h2>
              <p>Loading updates...</p>
            </div>
            <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
              ✕
            </button>
          </div>
          <div className="changelog-loading">
            <div className="loading-spinner"></div>
            <p>Loading changelog...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="changelog-overlay" onClick={onClose}>
        <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
          <div className="changelog-header">
            <div className="changelog-title">
              <h2>📋 Changelog</h2>
              <p>What's new in NAROOP</p>
            </div>
            <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
              ✕
            </button>
          </div>
          <div className="changelog-error">
            <div className="error-icon">⚠️</div>
            <h3>Unable to Load Updates</h3>
            <p>{error}</p>
            <button className="retry-btn" onClick={() => window.location.reload()}>
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="changelog-overlay" onClick={onClose}>
      <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
        <div className="changelog-header">
          <div className="changelog-title">
            <h2>📋 Changelog</h2>
            <p>What's new in NAROOP</p>
          </div>
          <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
            ✕
          </button>
        </div>

        <div className="changelog-filters">
          <button
            className={`filter-btn ${selectedCategory === 'all' ? 'active' : ''}`}
            onClick={() => setSelectedCategory('all')}
          >
            All Updates
          </button>
          {categories.map(category => (
            <button
              key={category}
              className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {CATEGORY_INFO[category].icon} {CATEGORY_INFO[category].label}
            </button>
          ))}
        </div>

        <div className="changelog-content">
          {filteredEntries.length === 0 ? (
            <div className="changelog-empty">
              <div className="empty-icon">🚀</div>
              <h3>Welcome to NAROOP!</h3>
              <p>
                This is where you'll find all the latest updates, new features, and improvements
                to the NAROOP platform. As we continue to grow and enhance your experience,
                all changes will be documented here.
              </p>
              <p>
                <strong>You're among our founding members!</strong> Thank you for being part
                of our journey from the very beginning.
              </p>
              <div className="coming-soon">
                <h4>Coming Soon:</h4>
                <ul>
                  <li>Platform updates and new features</li>
                  <li>Community enhancements</li>
                  <li>Bug fixes and improvements</li>
                  <li>Security updates</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="changelog-timeline">
              {filteredEntries.map((entry, index) => (
                <div key={entry.id} className="changelog-entry">
                  <div className="entry-marker">
                    <div 
                      className="marker-dot"
                      style={{ backgroundColor: CATEGORY_INFO[entry.category]?.color || '#3498db' }}
                    >
                      {CATEGORY_INFO[entry.category]?.icon || '📝'}
                    </div>
                    {index < filteredEntries.length - 1 && <div className="marker-line"></div>}
                  </div>

                  <div className="entry-content">
                    <div className="entry-header">
                      <div className="entry-version">
                        <span className="version-badge">v{entry.version}</span>
                        <span className="category-badge" style={{ 
                          backgroundColor: CATEGORY_INFO[entry.category]?.color || '#3498db' 
                        }}>
                          {CATEGORY_INFO[entry.category]?.icon} {CATEGORY_INFO[entry.category]?.label}
                        </span>
                      </div>
                      <div className="entry-date">
                        {formatDate(entry.releaseDate)}
                      </div>
                    </div>

                    <h3 className="entry-title">{entry.title}</h3>
                    <p className="entry-description">{entry.description}</p>

                    {entry.changes && entry.changes.length > 0 && (
                      <div className="entry-changes">
                        <h4>Changes:</h4>
                        <ul>
                          {entry.changes.map((change, changeIndex) => (
                            <li key={changeIndex}>{change}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="changelog-footer">
          <p>
            Current version: <strong>{getFormattedVersion()}</strong>
          </p>
          <p className="footer-note">
            Stay tuned for more updates as we continue to improve NAROOP!
          </p>
        </div>
      </div>
    </div>
  );
};

export default Changelog;
