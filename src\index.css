:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #333;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #f7fafc 0%, #10b981 100%);
  color: #333;
  min-height: 100vh;
  min-width: 320px;
  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

#root {
  min-height: 100vh;
  background: inherit;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
  /* Optimize link performance */
  will-change: color;
  transition: color 0.2s ease;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 50px; /* Perfect pill shape for all buttons */
  border: 2px solid transparent;
  padding: 12px 24px; /* Fixed padding for perfect pill shape */
  font-size: 1em;
  font-weight: 600;
  font-family: inherit;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* Optimal height for pill shape */
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
