import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { collection, addDoc, query, orderBy, onSnapshot, doc, updateDoc, arrayUnion } from 'firebase/firestore';
import { db } from '../firebase';

const DIALOGUE_TOPICS = [
  {
    id: 'generational_wisdom',
    title: 'Generational Wisdom Exchange',
    description: 'Share experiences and learn across age groups',
    icon: '🤝',
    guidelines: [
      'Respect different generational perspectives',
      'Share personal experiences, not generalizations',
      'Listen to understand, not to respond',
      'Find common ground while acknowledging differences'
    ]
  },
  {
    id: 'economic_strategies',
    title: 'Economic Empowerment Strategies',
    description: 'Discuss wealth-building and financial literacy',
    icon: '💰',
    guidelines: [
      'Share practical financial advice',
      'Discuss both traditional and modern approaches',
      'Be supportive of different financial situations',
      'Focus on actionable strategies'
    ]
  },
  {
    id: 'education_paths',
    title: 'Education & Career Paths',
    description: 'Explore diverse routes to success',
    icon: '🎓',
    guidelines: [
      'Celebrate all forms of education and skill development',
      'Share career journey experiences',
      'Discuss both traditional and alternative paths',
      'Offer mentorship and guidance'
    ]
  },
  {
    id: 'community_building',
    title: 'Community Building & Activism',
    description: 'Unite around shared values and goals',
    icon: '✊🏾',
    guidelines: [
      'Focus on constructive action and solutions',
      'Respect different approaches to activism',
      'Build bridges, not walls',
      'Emphasize unity while respecting diversity'
    ]
  },
  {
    id: 'cultural_preservation',
    title: 'Cultural Heritage & Innovation',
    description: 'Balance tradition with progress',
    icon: '🌍',
    guidelines: [
      'Honor our rich cultural heritage',
      'Embrace innovation and change',
      'Share cultural knowledge and traditions',
      'Discuss how to pass culture to next generation'
    ]
  }
];

const PERSPECTIVE_TAGS = [
  { id: 'gen_z', label: 'Gen Z Perspective', color: '#FF6B6B' },
  { id: 'millennial', label: 'Millennial View', color: '#4ECDC4' },
  { id: 'gen_x', label: 'Gen X Experience', color: '#45B7D1' },
  { id: 'boomer', label: 'Boomer Wisdom', color: '#96CEB4' },
  { id: 'elder', label: 'Elder Knowledge', color: '#FFEAA7' },
  { id: 'parent', label: 'Parent Perspective', color: '#DDA0DD' },
  { id: 'student', label: 'Student Voice', color: '#98D8C8' },
  { id: 'professional', label: 'Professional Insight', color: '#F7DC6F' }
];

export default function CommunityDialogue() {
  const { currentUser } = useAuth();
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [discussions, setDiscussions] = useState([]);
  const [newDiscussion, setNewDiscussion] = useState('');
  const [selectedPerspectives, setSelectedPerspectives] = useState([]);
  const [showNewDiscussionForm, setShowNewDiscussionForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (selectedTopic) {
      loadDiscussions(selectedTopic.id);
    }
  }, [selectedTopic]);

  const loadDiscussions = (topicId) => {
    const discussionsRef = collection(db, 'discussions');
    const q = query(
      discussionsRef,
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const discussionData = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(discussion => discussion.topicId === topicId);
      setDiscussions(discussionData);
    });

    return unsubscribe;
  };

  const startNewDiscussion = async () => {
    if (!currentUser || !newDiscussion.trim() || !selectedTopic) return;

    setLoading(true);
    setError(null);
    try {
      await addDoc(collection(db, 'discussions'), {
        topicId: selectedTopic.id,
        title: newDiscussion,
        authorId: currentUser.uid,
        authorName: currentUser.displayName || currentUser.email,
        perspectives: selectedPerspectives,
        responses: [],
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        participantCount: 1,
        guidelines: selectedTopic.guidelines
      });

      setNewDiscussion('');
      setSelectedPerspectives([]);
      setShowNewDiscussionForm(false);
      loadDiscussions(selectedTopic.id); // Refresh the list
    } catch (error) {
      console.error('Error starting discussion:', error);
      setError('Failed to start discussion. Please check your connection and try again.');
    }
    setLoading(false);
  };

  const addResponse = async (discussionId, response, perspectives) => {
    if (!currentUser || !response.trim()) return;

    try {
      const discussionRef = doc(db, 'discussions', discussionId);
      await updateDoc(discussionRef, {
        responses: arrayUnion({
          id: Date.now().toString(),
          authorId: currentUser.uid,
          authorName: currentUser.displayName || currentUser.email,
          content: response,
          perspectives: perspectives,
          createdAt: new Date().toISOString(),
          reactions: { hearts: 0, thoughtful: 0, insightful: 0 }
        }),
        lastActivity: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error adding response:', error);
    }
  };

  const togglePerspective = (perspectiveId) => {
    setSelectedPerspectives(prev => 
      prev.includes(perspectiveId)
        ? prev.filter(id => id !== perspectiveId)
        : [...prev, perspectiveId]
    );
  };

  if (!selectedTopic) {
    return (
      <div className="community-dialogue">
        <div className="dialogue-header">
          <h2>🗣️ Community Dialogue</h2>
          <p>Bridge differences, build understanding, strengthen unity</p>
        </div>

        <div className="dialogue-topics">
          <h3>Choose a Discussion Topic</h3>
          <div className="topics-grid">
            {DIALOGUE_TOPICS.map(topic => (
              <div
                key={topic.id}
                className="topic-card"
                onClick={() => setSelectedTopic(topic)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setSelectedTopic(topic);
                  }
                }}
                aria-label={`Select ${topic.title} discussion topic`}
              >
                <div className="topic-icon">{topic.icon}</div>
                <h4>{topic.title}</h4>
                <p>{topic.description}</p>
                <div className="topic-guidelines">
                  <small>{topic.guidelines.length} community guidelines</small>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="dialogue-principles">
          <h3>🤝 Our Dialogue Principles</h3>
          <div className="principles-grid">
            <div className="principle">
              <h4>🎯 Seek Understanding</h4>
              <p>Listen to learn, not to win arguments</p>
            </div>
            <div className="principle">
              <h4>🌈 Embrace Diversity</h4>
              <p>Value different perspectives and experiences</p>
            </div>
            <div className="principle">
              <h4>🔗 Build Bridges</h4>
              <p>Find common ground while respecting differences</p>
            </div>
            <div className="principle">
              <h4>💪 Strengthen Unity</h4>
              <p>Focus on what brings us together as a community</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="community-dialogue">
      <div className="dialogue-header">
        <button 
          className="back-btn"
          onClick={() => setSelectedTopic(null)}
        >
          ← Back to Topics
        </button>
        <div className="topic-info">
          <h2>{selectedTopic.icon} {selectedTopic.title}</h2>
          <p>{selectedTopic.description}</p>
        </div>
      </div>

      <div className="topic-guidelines">
        <h3>💡 Guidelines for this discussion:</h3>
        <ul>
          {selectedTopic.guidelines.map((guideline, index) => (
            <li key={index}>{guideline}</li>
          ))}
        </ul>
      </div>

      <div className="discussions-section">
        <div className="discussions-header">
          <h3>Active Discussions ({discussions.length})</h3>
          <button
            className="start-discussion-btn"
            onClick={() => setShowNewDiscussionForm(true)}
            disabled={!currentUser}
            title={!currentUser ? "Please log in to start a discussion" : "Start a new community discussion"}
          >
            + Start New Discussion
          </button>
        </div>

        {showNewDiscussionForm && (
          <div className="discussion-form">
            <h4>💬 Start a New Discussion</h4>

            <div className="form-field">
              <label className="form-label" htmlFor="discussion-content">
                Discussion Topic *
              </label>
              <textarea
                id="discussion-content"
                value={newDiscussion}
                onChange={(e) => setNewDiscussion(e.target.value)}
                placeholder="What would you like to discuss? Share your thoughts, questions, or experiences. Be respectful and constructive in your approach."
                className="form-textarea"
                maxLength={500}
                required
              />
              <div className="character-count">{newDiscussion.length}/500</div>
            </div>

            <div className="form-field">
              <label className="form-label">
                Tag Your Perspective (Optional)
              </label>
              <div className="form-helper-text">
                Help others understand your viewpoint by selecting relevant tags
              </div>
              <div className="perspective-tags">
                {PERSPECTIVE_TAGS.map(tag => (
                  <button
                    key={tag.id}
                    type="button"
                    className={`perspective-tag ${selectedPerspectives.includes(tag.id) ? 'selected' : ''}`}
                    style={{ backgroundColor: selectedPerspectives.includes(tag.id) ? tag.color : '#f0f0f0' }}
                    onClick={() => togglePerspective(tag.id)}
                  >
                    {tag.label}
                  </button>
                ))}
              </div>
            </div>

            {error && (
              <div className="form-error-message" style={{ marginBottom: '1rem' }}>
                {error}
              </div>
            )}

            <div className="form-actions">
              <button
                onClick={startNewDiscussion}
                disabled={loading || !newDiscussion.trim()}
                className={`submit-btn ${loading ? 'loading' : ''}`}
              >
                {loading ? 'Starting...' : '🗣️ Start Discussion'}
              </button>
              <button
                onClick={() => {
                  setShowNewDiscussionForm(false);
                  setError(null);
                }}
                className="cancel-btn"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="discussions-list">
          {discussions.length === 0 ? (
            <div className="no-discussions">
              <p>No discussions yet. Be the first to start a meaningful conversation!</p>
            </div>
          ) : (
            discussions.map(discussion => (
              <DiscussionCard 
                key={discussion.id} 
                discussion={discussion} 
                onAddResponse={addResponse}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

function DiscussionCard({ discussion, onAddResponse }) {
  const [showResponses, setShowResponses] = useState(false);
  const [newResponse, setNewResponse] = useState('');
  const [responsePerspectives, setResponsePerspectives] = useState([]);

  const toggleResponsePerspective = (perspectiveId) => {
    setResponsePerspectives(prev => 
      prev.includes(perspectiveId)
        ? prev.filter(id => id !== perspectiveId)
        : [...prev, perspectiveId]
    );
  };

  const handleAddResponse = () => {
    if (newResponse.trim()) {
      onAddResponse(discussion.id, newResponse, responsePerspectives);
      setNewResponse('');
      setResponsePerspectives([]);
    }
  };

  return (
    <div className="discussion-card">
      <div className="discussion-header">
        <h4>{discussion.title}</h4>
        <div className="discussion-meta">
          <span>by {discussion.authorName}</span>
          <span>{new Date(discussion.createdAt).toLocaleDateString()}</span>
        </div>
      </div>

      {discussion.perspectives && discussion.perspectives.length > 0 && (
        <div className="discussion-perspectives">
          {discussion.perspectives.map(perspectiveId => {
            const tag = PERSPECTIVE_TAGS.find(t => t.id === perspectiveId);
            return tag ? (
              <span 
                key={perspectiveId} 
                className="perspective-badge"
                style={{ backgroundColor: tag.color }}
              >
                {tag.label}
              </span>
            ) : null;
          })}
        </div>
      )}

      <div className="discussion-stats">
        <span>{discussion.responses?.length || 0} responses</span>
        <span>Last activity: {new Date(discussion.lastActivity).toLocaleDateString()}</span>
      </div>

      <div className="discussion-actions">
        <button 
          onClick={() => setShowResponses(!showResponses)}
          className="view-responses-btn"
        >
          {showResponses ? 'Hide' : 'View'} Responses
        </button>
      </div>

      {showResponses && (
        <div className="responses-section">
          <div className="responses-list">
            {discussion.responses?.map(response => (
              <div key={response.id} className="response-item">
                <div className="response-header">
                  <strong>{response.authorName}</strong>
                  <span>{new Date(response.createdAt).toLocaleDateString()}</span>
                </div>
                <p>{response.content}</p>
                {response.perspectives && response.perspectives.length > 0 && (
                  <div className="response-perspectives">
                    {response.perspectives.map(perspectiveId => {
                      const tag = PERSPECTIVE_TAGS.find(t => t.id === perspectiveId);
                      return tag ? (
                        <span 
                          key={perspectiveId} 
                          className="perspective-badge small"
                          style={{ backgroundColor: tag.color }}
                        >
                          {tag.label}
                        </span>
                      ) : null;
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="add-response">
            <textarea
              value={newResponse}
              onChange={(e) => setNewResponse(e.target.value)}
              placeholder="Share your thoughts respectfully..."
              className="response-input"
              maxLength={300}
            />
            
            <div className="response-perspective-selector">
              <div className="perspective-tags">
                {PERSPECTIVE_TAGS.slice(0, 4).map(tag => (
                  <button
                    key={tag.id}
                    className={`perspective-tag small ${responsePerspectives.includes(tag.id) ? 'selected' : ''}`}
                    style={{ backgroundColor: responsePerspectives.includes(tag.id) ? tag.color : '#f0f0f0' }}
                    onClick={() => toggleResponsePerspective(tag.id)}
                  >
                    {tag.label}
                  </button>
                ))}
              </div>
            </div>

            <button 
              onClick={handleAddResponse}
              disabled={!newResponse.trim()}
              className="add-response-btn"
            >
              Add Response
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
