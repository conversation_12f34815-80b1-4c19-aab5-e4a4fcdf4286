/**
 * Update changelog with latest version
 * Run this to add the latest changelog entries to Firebase
 */

import { addV121Changelog } from './addV121Changelog';

export const updateChangelog = async () => {
  try {
    console.log('🚀 Updating changelog with latest entries...');
    
    // Add the latest version entry
    await addV121Changelog();
    
    console.log('✅ Changelog updated successfully!');
    console.log('📋 Latest version: v1.2.1');
    console.log('🎯 Changes: Mobile profile menu improvements');
    
  } catch (error) {
    console.error('❌ Error updating changelog:', error);
    throw error;
  }
};

// Auto-run if this file is imported directly
if (typeof window !== 'undefined') {
  // Make it available globally for console access
  window.updateChangelog = updateChangelog;
  console.log('💡 Run updateChangelog() in the console to add the latest changelog entry');
}
