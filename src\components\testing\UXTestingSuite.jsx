import React, { useState, useEffect } from 'react';
import Layout, { PageLayout, GridLayout, SectionLayout } from '../layout/Layout';
import { StoryCard, FeatureCard, CommunityCard } from '../ui/Card';
import Icon from '../ui/Icon';
import './UXTestingSuite.css';

const UXTestingSuite = () => {
  const [testResults, setTestResults] = useState({});
  const [currentTest, setCurrentTest] = useState(null);

  // Simulate test data
  const sampleStories = [
    {
      id: 1,
      title: "From Struggle to Success: My Entrepreneurial Journey",
      excerpt: "Growing up in the inner city, I never imagined I'd own my own business. This is the story of how community support and determination changed everything.",
      author: "<PERSON>",
      date: "2 days ago",
      image: "/api/placeholder/400/200",
      tags: ["Entrepreneurship", "Community", "Success"],
      likes: 47,
      isLiked: false
    },
    {
      id: 2,
      title: "Breaking Barriers in Tech: A Sister's Story",
      excerpt: "As one of the few Black women in my computer science program, I faced unique challenges. Here's how I overcame them and now mentor others.",
      author: "<PERSON><PERSON><PERSON>",
      date: "1 week ago",
      image: "/api/placeholder/400/200",
      tags: ["Technology", "Education", "Mentorship"],
      likes: 89,
      isLiked: true
    },
    {
      id: 3,
      title: "Community Garden: Growing More Than Vegetables",
      excerpt: "What started as a small neighborhood garden became a movement that brought our community together and created lasting change.",
      author: "Robert Davis",
      date: "3 days ago",
      image: "/api/placeholder/400/200",
      tags: ["Community", "Environment", "Unity"],
      likes: 34,
      isLiked: false
    }
  ];

  const sampleFeatures = [
    {
      title: "Share Your Story",
      description: "Tell your narrative and inspire others in the community with your journey of triumph and resilience.",
      icon: <Icon name="stories" size="large" color="primary" />,
      ctaText: "Start Writing"
    },
    {
      title: "Economic Hub",
      description: "Discover business opportunities, connect with Black-owned enterprises, and build economic empowerment.",
      icon: <Icon name="economic" size="large" color="secondary" />,
      ctaText: "Explore Opportunities"
    },
    {
      title: "Community Connect",
      description: "Join discussions, find support, and build meaningful connections within the African American community.",
      icon: <Icon name="connect" size="large" color="accent" />,
      ctaText: "Join Community"
    }
  ];

  const communityStats = [
    { value: "2,847", label: "Stories Shared" },
    { value: "15,392", label: "Members" },
    { value: "$1.2M", label: "Economic Impact" },
    { value: "127", label: "Cities" }
  ];

  const communityHighlights = [
    "New mentorship program launched",
    "Monthly community meetup scheduled",
    "Economic empowerment workshop series",
    "Youth leadership initiative started"
  ];

  // Test functions
  const runAccessibilityTest = () => {
    setCurrentTest('accessibility');
    // Simulate accessibility testing
    setTimeout(() => {
      setTestResults(prev => ({
        ...prev,
        accessibility: {
          score: 98,
          issues: [
            { type: 'warning', message: 'Consider adding more descriptive alt text for decorative images' },
            { type: 'info', message: 'All interactive elements meet 44px minimum touch target requirement' }
          ],
          passed: true
        }
      }));
      setCurrentTest(null);
    }, 2000);
  };

  const runResponsiveTest = () => {
    setCurrentTest('responsive');
    // Simulate responsive testing
    setTimeout(() => {
      setTestResults(prev => ({
        ...prev,
        responsive: {
          mobile: { score: 95, passed: true },
          tablet: { score: 97, passed: true },
          desktop: { score: 99, passed: true },
          issues: [
            { type: 'info', message: 'All breakpoints tested successfully' },
            { type: 'success', message: 'Touch targets optimized for mobile devices' }
          ]
        }
      }));
      setCurrentTest(null);
    }, 3000);
  };

  const runUsabilityTest = () => {
    setCurrentTest('usability');
    // Simulate usability testing
    setTimeout(() => {
      setTestResults(prev => ({
        ...prev,
        usability: {
          navigationClarity: 92,
          taskCompletion: 89,
          userSatisfaction: 94,
          issues: [
            { type: 'success', message: 'Navigation patterns are intuitive and culturally appropriate' },
            { type: 'info', message: 'Users successfully completed story sharing tasks' }
          ],
          passed: true
        }
      }));
      setCurrentTest(null);
    }, 2500);
  };

  const runPerformanceTest = () => {
    setCurrentTest('performance');
    // Simulate performance testing
    setTimeout(() => {
      setTestResults(prev => ({
        ...prev,
        performance: {
          loadTime: 2.1,
          firstContentfulPaint: 1.2,
          largestContentfulPaint: 2.8,
          cumulativeLayoutShift: 0.05,
          issues: [
            { type: 'success', message: 'Page load times under 3 seconds' },
            { type: 'info', message: 'Optimized images and efficient CSS loading' }
          ],
          passed: true
        }
      }));
      setCurrentTest(null);
    }, 1500);
  };

  return (
    <Layout showNavigation={false} containerSize="wide">
      <PageLayout
        title="NAROOP UX/UI Testing Suite"
        subtitle="Comprehensive testing and validation of the redesigned platform"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Testing', href: '/admin/testing' },
          { label: 'UX Suite' }
        ]}
        actions={
          <div className="ux-testing__actions">
            <button 
              className="ux-testing__run-all-btn"
              onClick={() => {
                runAccessibilityTest();
                setTimeout(runResponsiveTest, 2500);
                setTimeout(runUsabilityTest, 6000);
                setTimeout(runPerformanceTest, 9000);
              }}
            >
              Run All Tests
            </button>
          </div>
        }
      >
        {/* Test Controls */}
        <SectionLayout
          title="Test Controls"
          subtitle="Run individual tests to validate different aspects of the UX/UI redesign"
          variant="community"
        >
          <GridLayout columns={4} gap="medium">
            <div className="ux-testing__test-card">
              <h3>Accessibility Test</h3>
              <p>WCAG AA compliance, keyboard navigation, screen reader compatibility</p>
              <button 
                className="ux-testing__test-btn"
                onClick={runAccessibilityTest}
                disabled={currentTest === 'accessibility'}
              >
                {currentTest === 'accessibility' ? 'Testing...' : 'Run Test'}
              </button>
              {testResults.accessibility && (
                <div className={`ux-testing__result ${testResults.accessibility.passed ? 'success' : 'error'}`}>
                  Score: {testResults.accessibility.score}/100
                </div>
              )}
            </div>

            <div className="ux-testing__test-card">
              <h3>Responsive Design</h3>
              <p>Mobile-first design, touch targets, cross-device compatibility</p>
              <button 
                className="ux-testing__test-btn"
                onClick={runResponsiveTest}
                disabled={currentTest === 'responsive'}
              >
                {currentTest === 'responsive' ? 'Testing...' : 'Run Test'}
              </button>
              {testResults.responsive && (
                <div className="ux-testing__result success">
                  Mobile: {testResults.responsive.mobile.score}/100
                </div>
              )}
            </div>

            <div className="ux-testing__test-card">
              <h3>Usability Test</h3>
              <p>Navigation clarity, task completion, cultural appropriateness</p>
              <button 
                className="ux-testing__test-btn"
                onClick={runUsabilityTest}
                disabled={currentTest === 'usability'}
              >
                {currentTest === 'usability' ? 'Testing...' : 'Run Test'}
              </button>
              {testResults.usability && (
                <div className={`ux-testing__result ${testResults.usability.passed ? 'success' : 'error'}`}>
                  Satisfaction: {testResults.usability.userSatisfaction}/100
                </div>
              )}
            </div>

            <div className="ux-testing__test-card">
              <h3>Performance Test</h3>
              <p>Load times, Core Web Vitals, optimization metrics</p>
              <button 
                className="ux-testing__test-btn"
                onClick={runPerformanceTest}
                disabled={currentTest === 'performance'}
              >
                {currentTest === 'performance' ? 'Testing...' : 'Run Test'}
              </button>
              {testResults.performance && (
                <div className={`ux-testing__result ${testResults.performance.passed ? 'success' : 'error'}`}>
                  Load: {testResults.performance.loadTime}s
                </div>
              )}
            </div>
          </GridLayout>
        </SectionLayout>

        {/* Component Testing */}
        <SectionLayout
          title="Component Testing"
          subtitle="Test individual components with real data to validate design and functionality"
        >
          {/* Story Cards Test */}
          <div className="ux-testing__component-section">
            <h3>Story Cards</h3>
            <GridLayout columns="auto" gap="medium">
              {sampleStories.map(story => (
                <StoryCard
                  key={story.id}
                  title={story.title}
                  excerpt={story.excerpt}
                  author={story.author}
                  date={story.date}
                  image={story.image}
                  tags={story.tags}
                  likes={story.likes}
                  isLiked={story.isLiked}
                  onRead={() => console.log('Read story:', story.id)}
                  onLike={() => console.log('Like story:', story.id)}
                  onShare={() => console.log('Share story:', story.id)}
                />
              ))}
            </GridLayout>
          </div>

          {/* Feature Cards Test */}
          <div className="ux-testing__component-section">
            <h3>Feature Cards</h3>
            <GridLayout columns={3} gap="medium">
              {sampleFeatures.map((feature, index) => (
                <FeatureCard
                  key={index}
                  title={feature.title}
                  description={feature.description}
                  icon={feature.icon}
                  ctaText={feature.ctaText}
                  onCTA={() => console.log('CTA clicked:', feature.title)}
                />
              ))}
            </GridLayout>
          </div>

          {/* Community Card Test */}
          <div className="ux-testing__component-section">
            <h3>Community Card</h3>
            <GridLayout columns={1} gap="medium">
              <CommunityCard
                title="Community Impact"
                stats={communityStats}
                highlights={communityHighlights}
                onViewMore={() => console.log('View more clicked')}
              />
            </GridLayout>
          </div>
        </SectionLayout>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <SectionLayout
            title="Test Results"
            subtitle="Detailed results from UX/UI testing suite"
            variant="featured"
          >
            <div className="ux-testing__results">
              {Object.entries(testResults).map(([testName, result]) => (
                <div key={testName} className="ux-testing__result-section">
                  <h3>{testName.charAt(0).toUpperCase() + testName.slice(1)} Test Results</h3>
                  
                  {result.issues && (
                    <div className="ux-testing__issues">
                      {result.issues.map((issue, index) => (
                        <div key={index} className={`ux-testing__issue ux-testing__issue--${issue.type}`}>
                          {issue.message}
                        </div>
                      ))}
                    </div>
                  )}

                  {result.score && (
                    <div className="ux-testing__score">
                      Overall Score: {result.score}/100
                    </div>
                  )}

                  {result.mobile && (
                    <div className="ux-testing__responsive-scores">
                      <div>Mobile: {result.mobile.score}/100</div>
                      <div>Tablet: {result.tablet.score}/100</div>
                      <div>Desktop: {result.desktop.score}/100</div>
                    </div>
                  )}

                  {result.loadTime && (
                    <div className="ux-testing__performance-metrics">
                      <div>Load Time: {result.loadTime}s</div>
                      <div>First Contentful Paint: {result.firstContentfulPaint}s</div>
                      <div>Largest Contentful Paint: {result.largestContentfulPaint}s</div>
                      <div>Cumulative Layout Shift: {result.cumulativeLayoutShift}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </SectionLayout>
        )}
      </PageLayout>
    </Layout>
  );
};

export default UXTestingSuite;
