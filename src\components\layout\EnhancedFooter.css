/* NAROOP Enhanced Footer - Black & Gold Theme */

.enhanced-footer {
  background: linear-gradient(135deg, var(--color-heritage-black) 0%, var(--color-heritage-deep-gold) 100%);
  color: var(--color-heritage-cream);
  margin-top: auto;
}

.enhanced-footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Community Stats Section */
.enhanced-footer__stats {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  padding: var(--space-lg) 0;
  border-bottom: 2px solid var(--color-heritage-black);
}

.enhanced-footer__stats-title {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 var(--space-md) 0;
}

.enhanced-footer__stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  text-align: center;
}

.enhanced-footer__stat {
  padding: var(--space-sm);
  background: rgba(26, 26, 26, 0.1);
  border-radius: 12px;
  border: 2px solid var(--color-heritage-black);
}

.enhanced-footer__stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin-bottom: var(--space-xs);
}

.enhanced-footer__stat-label {
  font-size: 0.875rem;
  color: var(--color-heritage-black);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Newsletter Section */
.enhanced-footer__newsletter {
  background: var(--color-empowerment-amber);
  color: var(--color-heritage-black);
  padding: var(--space-lg) 0;
  border-bottom: 2px solid var(--color-heritage-black);
}

.enhanced-footer__newsletter-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
  align-items: center;
}

.enhanced-footer__newsletter-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--space-sm) 0;
  color: var(--color-heritage-black);
}

.enhanced-footer__newsletter-description {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
  color: var(--color-heritage-black);
  opacity: 0.9;
}

.enhanced-footer__newsletter-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.enhanced-footer__newsletter-input-group {
  display: flex;
  gap: var(--space-sm);
}

.enhanced-footer__newsletter-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid var(--color-heritage-black);
  border-radius: 8px;
  font-size: 1rem;
  background: var(--color-heritage-cream);
  color: var(--color-heritage-black);
  transition: all 0.3s ease;
}

.enhanced-footer__newsletter-input:focus {
  outline: none;
  border-color: var(--color-heritage-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

.enhanced-footer__newsletter-btn {
  background: var(--color-heritage-black);
  color: var(--color-heritage-gold);
  border: 2px solid var(--color-heritage-black);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 48px;
}

.enhanced-footer__newsletter-btn:hover:not(:disabled) {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  transform: translateY(-2px);
}

.enhanced-footer__newsletter-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.enhanced-footer__newsletter-success {
  background: var(--color-heritage-forest);
  color: var(--color-heritage-cream);
  padding: var(--space-sm);
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
}

.enhanced-footer__newsletter-error {
  background: var(--color-heritage-burgundy);
  color: var(--color-heritage-cream);
  padding: var(--space-sm);
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
}

/* Main Footer Content */
.enhanced-footer__main {
  padding: var(--space-xl) 0;
}

.enhanced-footer__content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-lg);
}

.enhanced-footer__section {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.enhanced-footer__section--heritage {
  grid-column: span 2;
}

.enhanced-footer__section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-heritage-gold);
  margin: 0;
  border-bottom: 2px solid var(--color-heritage-gold);
  padding-bottom: var(--space-xs);
}

.enhanced-footer__nav {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.enhanced-footer__link {
  color: var(--color-heritage-cream);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  padding: var(--space-xs) 0;
  border-radius: 4px;
}

.enhanced-footer__link:hover {
  color: var(--color-heritage-gold);
  padding-left: var(--space-sm);
}

/* Heritage Section */
.enhanced-footer__heritage-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.enhanced-footer__mission {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--color-heritage-cream);
  margin: 0;
}

.enhanced-footer__values {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.enhanced-footer__value {
  font-size: 0.8rem;
  color: var(--color-heritage-cream);
  opacity: 0.9;
  padding: var(--space-xs);
  background: rgba(255, 248, 220, 0.1);
  border-radius: 6px;
  border-left: 3px solid var(--color-heritage-gold);
}

/* Social Links */
.enhanced-footer__social-links {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
}

.enhanced-footer__social-link {
  width: 44px;
  height: 44px;
  background: transparent;
  border: 2px solid var(--color-heritage-cream);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-heritage-cream);
  transition: all 0.3s ease;
  text-decoration: none;
}

.enhanced-footer__social-link:hover {
  background: var(--color-heritage-gold);
  border-color: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  transform: translateY(-2px);
}

.enhanced-footer__social-link svg {
  width: 20px;
  height: 20px;
}

.enhanced-footer__contact-info {
  font-size: 0.875rem;
  color: var(--color-heritage-cream);
}

.enhanced-footer__contact-info p {
  margin: 0 0 var(--space-xs) 0;
}

.enhanced-footer__contact-link {
  color: var(--color-heritage-gold);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.enhanced-footer__contact-link:hover {
  color: var(--color-empowerment-amber);
}

/* Footer Bottom */
.enhanced-footer__bottom {
  background: var(--color-heritage-black);
  padding: var(--space-md) 0;
  border-top: 2px solid var(--color-heritage-gold);
}

.enhanced-footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-md);
}

.enhanced-footer__copyright {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.enhanced-footer__copyright p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--color-heritage-cream);
}

.enhanced-footer__version {
  font-size: 0.75rem;
  color: var(--color-heritage-gold);
  font-weight: 600;
}

.enhanced-footer__tagline {
  text-align: right;
  max-width: 400px;
}

.enhanced-footer__tagline p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--color-heritage-cream);
  font-style: italic;
  opacity: 0.9;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .enhanced-footer__content {
    grid-template-columns: repeat(2, 1fr);
  }

  .enhanced-footer__section--heritage {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .enhanced-footer__container {
    padding: 0 var(--space-sm);
  }

  .enhanced-footer__stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-sm);
  }

  .enhanced-footer__newsletter-content {
    grid-template-columns: 1fr;
    gap: var(--space-md);
    text-align: center;
  }

  .enhanced-footer__content {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .enhanced-footer__section--heritage {
    grid-column: span 1;
  }

  .enhanced-footer__bottom-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }

  .enhanced-footer__tagline {
    text-align: center;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .enhanced-footer__stats-grid {
    grid-template-columns: 1fr;
  }

  .enhanced-footer__newsletter-input-group {
    flex-direction: column;
  }

  .enhanced-footer__social-links {
    justify-content: center;
  }

  .enhanced-footer__stat-value {
    font-size: 1.5rem;
  }
}

/* Focus States for Accessibility */
.enhanced-footer__link:focus,
.enhanced-footer__social-link:focus,
.enhanced-footer__contact-link:focus {
  outline: 2px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .enhanced-footer__link,
  .enhanced-footer__social-link,
  .enhanced-footer__newsletter-btn {
    transition: none;
  }

  .enhanced-footer__social-link:hover,
  .enhanced-footer__newsletter-btn:hover {
    transform: none;
  }
}
