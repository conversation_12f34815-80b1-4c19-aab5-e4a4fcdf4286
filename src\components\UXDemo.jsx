import React from 'react';
import Layout, { PageLayout, GridLayout, SectionLayout } from './layout/Layout';
import { StoryCard, FeatureCard, CommunityCard } from './ui/Card';
import Icon from './ui/Icon';

const UXDemo = () => {
  // Sample data for demonstration
  const sampleStories = [
    {
      id: 1,
      title: "From Struggle to Success: My Entrepreneurial Journey",
      excerpt: "Growing up in the inner city, I never imagined I'd own my own business. This is the story of how community support and determination changed everything.",
      author: "<PERSON>",
      date: "2 days ago",
      image: "https://via.placeholder.com/400x200/FFD700/1A1A1A?text=Success+Story",
      tags: ["Entrepreneurship", "Community", "Success"],
      likes: 47,
      isLiked: false
    },
    {
      id: 2,
      title: "Breaking Barriers in Tech: A Sister's Story",
      excerpt: "As one of the few Black women in my computer science program, I faced unique challenges. Here's how I overcame them and now mentor others.",
      author: "<PERSON><PERSON><PERSON>",
      date: "1 week ago",
      image: "https://via.placeholder.com/400x200/355E3B/FFF8DC?text=Tech+Story",
      tags: ["Technology", "Education", "Mentorship"],
      likes: 89,
      isLiked: true
    }
  ];

  const sampleFeatures = [
    {
      title: "Share Your Story",
      description: "Tell your narrative and inspire others in the community with your journey of triumph and resilience.",
      icon: <Icon name="stories" size="large" color="primary" />,
      ctaText: "Start Writing"
    },
    {
      title: "Economic Hub",
      description: "Discover business opportunities, connect with Black-owned enterprises, and build economic empowerment.",
      icon: <Icon name="economic" size="large" color="secondary" />,
      ctaText: "Explore Opportunities"
    },
    {
      title: "Community Connect",
      description: "Join discussions, find support, and build meaningful connections within the African American community.",
      icon: <Icon name="connect" size="large" color="accent" />,
      ctaText: "Join Community"
    }
  ];

  const communityStats = [
    { value: "2,847", label: "Stories Shared" },
    { value: "15,392", label: "Members" },
    { value: "$1.2M", label: "Economic Impact" },
    { value: "127", label: "Cities" }
  ];

  const communityHighlights = [
    "New mentorship program launched",
    "Monthly community meetup scheduled",
    "Economic empowerment workshop series",
    "Youth leadership initiative started"
  ];

  return (
    <Layout showNavigation={false} containerSize="wide">
      <PageLayout
        title="NAROOP UX/UI Redesign Demo"
        subtitle="Experience the new black and gold themed design system with improved navigation and cultural authenticity"
        breadcrumbs={[
          { label: 'Home', href: '/' },
          { label: 'Demo' }
        ]}
        actions={
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button 
              style={{
                background: 'var(--color-heritage-gold)',
                color: 'var(--color-heritage-black)',
                border: '2px solid var(--color-heritage-black)',
                borderRadius: '24px',
                padding: '0.75rem 1.5rem',
                fontWeight: '600',
                cursor: 'pointer'
              }}
              onClick={() => window.location.href = '/'}
            >
              Back to App
            </button>
          </div>
        }
      >
        {/* Icon System Demo */}
        <SectionLayout
          title="New Icon System"
          subtitle="Culturally appropriate SVG icons replacing emoji for professional appearance"
          variant="community"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
            gap: '1rem',
            textAlign: 'center'
          }}>
            <div>
              <Icon name="home" size="large" color="primary" />
              <p>Home</p>
            </div>
            <div>
              <Icon name="stories" size="large" color="secondary" />
              <p>Stories</p>
            </div>
            <div>
              <Icon name="connect" size="large" color="accent" />
              <p>Connect</p>
            </div>
            <div>
              <Icon name="economic" size="large" color="heritage" />
              <p>Economic</p>
            </div>
            <div>
              <Icon name="excellence" size="large" color="primary" />
              <p>Excellence</p>
            </div>
            <div>
              <Icon name="heritage" size="large" color="accent" />
              <p>Heritage</p>
            </div>
          </div>
        </SectionLayout>

        {/* Card System Demo */}
        <SectionLayout
          title="Story Cards"
          subtitle="Enhanced story presentation with cultural color accents and improved readability"
        >
          <GridLayout columns="auto" gap="medium">
            {sampleStories.map(story => (
              <StoryCard
                key={story.id}
                title={story.title}
                excerpt={story.excerpt}
                author={story.author}
                date={story.date}
                image={story.image}
                tags={story.tags}
                likes={story.likes}
                isLiked={story.isLiked}
                onRead={() => alert('Read story: ' + story.title)}
                onLike={() => alert('Like story: ' + story.title)}
                onShare={() => alert('Share story: ' + story.title)}
              />
            ))}
          </GridLayout>
        </SectionLayout>

        {/* Feature Cards Demo */}
        <SectionLayout
          title="Feature Cards"
          subtitle="Platform features with empowerment-focused design and clear call-to-actions"
          variant="featured"
        >
          <GridLayout columns={3} gap="medium">
            {sampleFeatures.map((feature, index) => (
              <FeatureCard
                key={index}
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                ctaText={feature.ctaText}
                onCTA={() => alert('CTA clicked: ' + feature.title)}
              />
            ))}
          </GridLayout>
        </SectionLayout>

        {/* Community Card Demo */}
        <SectionLayout
          title="Community Impact"
          subtitle="Highlighting community achievements and engagement metrics"
        >
          <GridLayout columns={1} gap="medium">
            <CommunityCard
              title="Community Impact Dashboard"
              stats={communityStats}
              highlights={communityHighlights}
              onViewMore={() => alert('View more community details')}
            />
          </GridLayout>
        </SectionLayout>

        {/* Color Palette Demo */}
        <SectionLayout
          title="Black & Gold Color Palette"
          subtitle="Culturally authentic colors honoring African American heritage and Alpha Phi Alpha tradition"
          variant="community"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem'
          }}>
            <div style={{ 
              background: 'var(--color-heritage-black)', 
              color: 'var(--color-heritage-gold)',
              padding: '2rem',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px solid var(--color-heritage-gold)'
            }}>
              <h3>Heritage Black</h3>
              <p>#1A1A1A</p>
              <small>Strength & Heritage</small>
            </div>
            <div style={{ 
              background: 'var(--color-heritage-gold)', 
              color: 'var(--color-heritage-black)',
              padding: '2rem',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px solid var(--color-heritage-black)'
            }}>
              <h3>Heritage Gold</h3>
              <p>#FFD700</p>
              <small>Excellence & Achievement</small>
            </div>
            <div style={{ 
              background: 'var(--color-heritage-forest)', 
              color: 'var(--color-heritage-cream)',
              padding: '2rem',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px solid var(--color-heritage-cream)'
            }}>
              <h3>Heritage Forest</h3>
              <p>#355E3B</p>
              <small>Growth & Hope</small>
            </div>
            <div style={{ 
              background: 'var(--color-empowerment-amber)', 
              color: 'var(--color-heritage-black)',
              padding: '2rem',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px solid var(--color-heritage-black)'
            }}>
              <h3>Empowerment Amber</h3>
              <p>#FFBF00</p>
              <small>Joy & Celebration</small>
            </div>
          </div>
        </SectionLayout>

        {/* Design Principles */}
        <SectionLayout
          title="Design Principles"
          subtitle="Core principles guiding the NAROOP UX/UI redesign"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
            gap: '1.5rem'
          }}>
            <div style={{ 
              background: 'var(--color-heritage-cream)',
              border: '2px solid var(--color-heritage-gold)',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h4 style={{ color: 'var(--color-heritage-black)', marginTop: 0 }}>Cultural Authenticity</h4>
              <p>Every design decision honors African American heritage and community values.</p>
            </div>
            <div style={{ 
              background: 'var(--color-heritage-cream)',
              border: '2px solid var(--color-heritage-forest)',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h4 style={{ color: 'var(--color-heritage-black)', marginTop: 0 }}>Accessibility First</h4>
              <p>WCAG AA compliance ensures the platform is inclusive for all community members.</p>
            </div>
            <div style={{ 
              background: 'var(--color-heritage-cream)',
              border: '2px solid var(--color-empowerment-amber)',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h4 style={{ color: 'var(--color-heritage-black)', marginTop: 0 }}>Mobile-First</h4>
              <p>Touch-friendly interfaces optimized for mobile devices with progressive enhancement.</p>
            </div>
            <div style={{ 
              background: 'var(--color-heritage-cream)',
              border: '2px solid var(--color-heritage-burgundy)',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h4 style={{ color: 'var(--color-heritage-black)', marginTop: 0 }}>Community-Centered</h4>
              <p>Design supports storytelling, economic empowerment, and community connection.</p>
            </div>
          </div>
        </SectionLayout>
      </PageLayout>
    </Layout>
  );
};

export default UXDemo;
