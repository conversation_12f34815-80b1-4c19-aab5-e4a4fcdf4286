/* Admin Dashboard Styles */
.admin-dashboard-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(89, 28, 40, 0.8); /* Dark maroon overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: var(--space-md);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.admin-dashboard-content {
  background: #FDFBF5; /* Light cream background */
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 1200px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(89, 28, 40, 0.4);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(89, 28, 40, 0.1);
}

/* Header */
.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-bottom: 3px solid var(--color-accent-red);
}

.admin-header h2 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: 700;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.role-badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge.platform_owner {
  background: linear-gradient(135deg, var(--color-accent-red), var(--color-heritage-gold));
  color: var(--color-light);
}

.role-badge.super_admin {
  background: var(--color-accent-red);
  color: var(--color-light);
}

.role-badge.admin {
  background: var(--color-primary);
  color: var(--color-light);
}

.role-badge.moderator {
  background: var(--color-accent-green);
  color: var(--color-light);
}

.role-badge.community_helper {
  background: var(--color-accent-emerald);
  color: var(--color-light);
}

.close-admin-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-admin-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Tabs */
.admin-tabs {
  display: flex;
  background: var(--color-gray-light);
  border-bottom: 1px solid rgba(184, 134, 11, 0.2);
  overflow-x: auto;
}

.admin-tab {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  border: none;
  background: transparent;
  color: var(--color-gray-medium);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  min-width: 120px;
}

.admin-tab:hover {
  background: rgba(184, 134, 11, 0.1);
  color: var(--color-primary);
}

.admin-tab.active {
  background: var(--color-light);
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  font-weight: 600;
}

/* Body */
.admin-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

/* Overview */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.admin-stat-card {
  background: linear-gradient(135deg, var(--color-light), rgba(184, 134, 11, 0.05));
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: var(--transition-normal);
}

.admin-stat-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: var(--text-3xl);
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  border-radius: var(--radius-full);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content h3 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--color-primary);
}

.stat-content p {
  margin: 0;
  color: var(--color-gray-medium);
  font-weight: 500;
}

/* Quick Actions */
.admin-quick-actions {
  margin-bottom: var(--space-xl);
}

.admin-quick-actions h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-lg);
  font-size: var(--text-xl);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
}

.quick-action-btn {
  background: transparent;
  border: 2px solid #F7D046; /* Warm yellow outline for admin actions */
  color: #F7D046;
  padding: var(--space-md);
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* Consistent gap for icon-to-text spacing */
  min-height: 44px;
}

.quick-action-btn:hover {
  background: #F7D046; /* Yellow fill on hover for admin actions */
  color: #591C28;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.3);
}

/* Recent Activity */
.admin-recent-activity h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-lg);
  font-size: var(--text-xl);
}

.activity-list {
  background: rgba(184, 134, 11, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  border-bottom: 1px solid rgba(184, 134, 11, 0.1);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: var(--text-lg);
  background: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(184, 134, 11, 0.2);
}

.activity-content {
  flex: 1;
}

.activity-action {
  margin: 0;
  font-weight: 600;
  color: var(--color-dark);
}

.activity-details {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--color-gray-medium);
}

/* Content Items */
.content-list,
.reports-list,
.admin-users-list,
.logs-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.content-item,
.report-item,
.admin-user-item,
.log-item {
  background: rgba(184, 134, 11, 0.05);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-normal);
}

.content-item:hover,
.report-item:hover,
.admin-user-item:hover,
.log-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.content-header,
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.content-header h5,
.report-header h5 {
  margin: 0;
  color: var(--color-primary);
  font-size: var(--text-lg);
}

.status-badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: var(--color-accent-yellow);
  color: var(--color-dark);
}

.status-badge.flagged {
  background: var(--color-accent-red);
  color: var(--color-light);
}

.content-preview {
  color: var(--color-gray-medium);
  margin-bottom: var(--space-md);
  line-height: 1.5;
}

.content-meta,
.report-meta {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
  font-size: var(--text-sm);
  color: var(--color-gray-medium);
}

.content-actions,
.report-actions,
.admin-user-actions {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.approve-btn,
.dismiss-btn {
  background: var(--color-accent-green);
  color: var(--color-light);
}

.reject-btn,
.remove-btn,
.remove-role-btn {
  background: var(--color-accent-red);
  color: var(--color-light);
}

.view-btn,
.warn-btn,
.modify-role-btn {
  background: var(--color-primary);
  color: var(--color-light);
}

.approve-btn,
.reject-btn,
.view-btn,
.dismiss-btn,
.remove-btn,
.warn-btn,
.modify-role-btn,
.remove-role-btn {
  border: none;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.approve-btn:hover,
.reject-btn:hover,
.view-btn:hover,
.dismiss-btn:hover,
.remove-btn:hover,
.warn-btn:hover,
.modify-role-btn:hover,
.remove-role-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Admin Users */
.admin-user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.admin-avatar {
  font-size: var(--text-2xl);
  background: var(--color-light);
  border-radius: var(--radius-full);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-primary);
}

.admin-details h5 {
  margin: 0;
  color: var(--color-primary);
}

.admin-email {
  margin: 0;
  color: var(--color-gray-medium);
  font-size: var(--text-sm);
}

.admin-user-meta {
  text-align: right;
  font-size: var(--text-sm);
  color: var(--color-gray-medium);
}

/* Access Denied */
.access-denied {
  text-align: center;
  padding: var(--space-2xl);
}

.access-denied h2 {
  color: var(--color-accent-red);
  margin-bottom: var(--space-lg);
}

.close-btn {
  background: var(--color-primary);
  color: var(--color-light);
  border: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--color-gray-medium);
  font-size: var(--text-lg);
}

/* User Management Styles */
.admin-user-management {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.user-search-section {
  background: rgba(184, 134, 11, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border-left: 4px solid var(--color-primary);
}

.user-search-form {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.user-search-input {
  flex: 1;
  padding: var(--space-sm);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
}

.search-user-btn {
  background: var(--color-primary);
  color: var(--color-light);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.search-user-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.moderator-assignment-section {
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1), rgba(230, 57, 70, 0.05));
  border: 2px solid var(--color-accent-red);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.moderator-assignment-section h4 {
  color: var(--color-accent-red);
  margin-bottom: var(--space-md);
}

.role-descriptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.role-desc {
  background: var(--color-light);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--color-primary);
  font-size: var(--text-sm);
}

.user-actions-guide {
  background: rgba(184, 134, 11, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.action-card {
  background: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  text-align: center;
  transition: var(--transition-normal);
}

.action-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.action-icon {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-sm);
}

.action-card h5 {
  margin: 0 0 var(--space-xs) 0;
  color: var(--color-primary);
}

.action-card p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--color-gray-medium);
}

.user-management-tips {
  background: rgba(184, 134, 11, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-top: var(--space-md);
}

.user-management-tips h5 {
  color: var(--color-primary);
  margin-bottom: var(--space-sm);
}

.user-management-tips ul {
  margin: 0;
  padding-left: var(--space-lg);
  color: var(--color-gray-medium);
}

.user-management-tips li {
  margin-bottom: var(--space-xs);
  font-size: var(--text-sm);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-dashboard-modal {
    padding: var(--space-sm);
  }
  
  .admin-dashboard-content {
    max-height: 98vh;
  }
  
  .admin-header {
    padding: var(--space-md);
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .admin-body {
    padding: var(--space-md);
  }
  
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .content-actions,
  .report-actions,
  .admin-user-actions {
    flex-direction: column;
  }
  
  .admin-user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }
  
  .admin-tabs {
    overflow-x: auto;
  }
  
  .admin-tab {
    min-width: 100px;
    padding: var(--space-sm);
  }
}
