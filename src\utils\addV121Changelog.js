/**
 * Version 1.2.1 changelog entry
 * Mobile dropdown improvements and UI fixes
 */

import { db } from '../firebase';
import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';

export const addV121Changelog = async () => {
  try {
    console.log('🚀 Adding v1.2.1 changelog entry...');
    
    const changelogRef = collection(db, 'changelog');
    
    // Check if v1.2.1 already exists
    const v121Query = query(changelogRef, where('version', '==', '1.2.1'));
    const v121Snapshot = await getDocs(v121Query);
    
    if (v121Snapshot.empty) {
      // Add v1.2.1 entry
      const v121Entry = {
        version: '1.2.1',
        title: 'Mobile Profile Menu Improvements - Better Mobile Experience! 📱',
        description: 'We\'ve significantly improved the mobile profile dropdown menu to make it more user-friendly and accessible. The menu now displays properly on all mobile devices with better spacing and positioning.',
        category: 'improvement',
        releaseDate: new Date().toISOString(),
        changes: [
          'Fixed mobile profile dropdown menu positioning to prevent cutoff',
          'Improved dropdown menu width and spacing on mobile devices',
          'Enhanced touch-friendly interaction areas for better mobile usability',
          'Removed unwanted pill-shaped borders that were causing display issues',
          'Better centered content alignment within the profile dropdown',
          'Optimized dropdown positioning for different screen sizes',
          'Improved visual consistency across mobile and desktop views',
          'Enhanced accessibility with proper touch targets and spacing'
        ],
        isBreaking: false,
        timestamp: Date.now()
      };

      await addDoc(changelogRef, v121Entry);
      console.log('✅ v1.2.1 changelog entry added successfully');
    } else {
      console.log('ℹ️ v1.2.1 changelog entry already exists');
    }
  } catch (error) {
    console.error('❌ Error adding v1.2.1 changelog entry:', error);
    throw error;
  }
};
