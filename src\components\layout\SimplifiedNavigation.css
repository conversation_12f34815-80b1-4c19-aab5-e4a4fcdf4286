/* NAROOP Simplified Navigation - Black & Gold Theme */

.simplified-navigation {
  position: relative;
  z-index: 900;
}

/* Primary Navigation */
.simplified-navigation__primary {
  background: var(--color-heritage-cream);
  border-bottom: 2px solid var(--color-heritage-gold);
  box-shadow: 0 2px 8px rgba(26, 26, 26, 0.1);
  padding: var(--space-md) 0;
}

.simplified-navigation__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
}

.simplified-navigation__primary-item {
  background: transparent;
  border: 2px solid var(--color-heritage-gold);
  padding: var(--space-sm) var(--space-md);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  border-radius: 24px; /* Pill shape */
  min-height: 44px;
  display: flex;
  flex-direction: row; /* Horizontal layout */
  align-items: center;
  justify-content: flex-start;
  gap: var(--space-sm);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-width: 120px; /* Ensure consistent minimum width */
}

.simplified-navigation__primary-item:hover {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(247, 208, 70, 0.3);
}

.simplified-navigation__primary-item--active {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  border-color: var(--color-heritage-gold);
}

.simplified-navigation__icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
  color: inherit;
  flex-shrink: 0;
}

.simplified-navigation__label {
  font-size: 0.875rem;
  font-weight: 600;
  color: inherit;
  margin: 0;
  flex-shrink: 0;
}

.simplified-navigation__primary-item--active .simplified-navigation__label {
  color: var(--color-heritage-black);
}

.simplified-navigation__description {
  display: none; /* Hide description for compact pill design */
}

.simplified-navigation__primary-item--active .simplified-navigation__description {
  display: none; /* Hide description for compact pill design */
}

/* Secondary Navigation Overlay */
.simplified-navigation__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

.simplified-navigation__secondary {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-gold);
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(26, 26, 26, 0.3);
  z-index: 999;
  min-width: 320px;
  max-width: 480px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInScale 0.3s ease-out;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.simplified-navigation__secondary-header {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-empowerment-amber) 100%);
  color: var(--color-heritage-black);
  padding: var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.simplified-navigation__secondary-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.simplified-navigation__close-btn {
  background: transparent;
  border: 2px solid var(--color-heritage-black);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-heritage-black);
}

.simplified-navigation__close-btn:hover {
  background: var(--color-heritage-black);
  color: var(--color-heritage-gold);
  transform: rotate(90deg);
}

.simplified-navigation__close-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.simplified-navigation__secondary-content {
  padding: var(--space-sm) 0;
  max-height: 60vh;
  overflow-y: auto;
}

.simplified-navigation__secondary-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: var(--space-md);
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--color-prosperity-champagne);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--space-xs);
}

.simplified-navigation__secondary-item:last-child {
  border-bottom: none;
}

.simplified-navigation__secondary-item:hover {
  background: var(--color-prosperity-champagne);
  padding-left: var(--space-lg);
}

.simplified-navigation__secondary-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0;
}

.simplified-navigation__secondary-description {
  font-size: 0.875rem;
  color: var(--color-heritage-forest);
  margin: 0;
  opacity: 0.8;
}

/* Desktop Hover Styles */
@media (min-width: 769px) {
  .simplified-navigation__secondary {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    transform: none;
    background: var(--color-heritage-cream);
    border: 2px solid var(--color-heritage-gold);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(26, 26, 26, 0.2);
    z-index: 999;
    min-width: auto;
    max-width: none;
    max-height: 400px;
    margin-top: var(--space-xs);
    animation: slideDown 0.2s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .simplified-navigation__secondary-header {
    padding: var(--space-sm) var(--space-md);
  }

  .simplified-navigation__secondary-title {
    font-size: 1rem;
  }

  .simplified-navigation__close-btn {
    display: none; /* Hide close button on desktop */
  }

  .simplified-navigation__secondary-content {
    padding: var(--space-sm);
    max-height: 300px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-xs);
  }

  .simplified-navigation__secondary-item {
    padding: var(--space-sm);
    text-align: left;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .simplified-navigation__secondary-item:hover {
    background: var(--color-prosperity-champagne);
    transform: translateY(-1px);
  }

  .simplified-navigation__overlay {
    display: none; /* Hide overlay on desktop */
  }

  /* Position secondary nav relative to primary nav */
  .simplified-navigation {
    position: relative;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .simplified-navigation__container {
    padding: 0 var(--space-sm);
    gap: var(--space-xs);
    flex-wrap: wrap;
    justify-content: center;
  }

  .simplified-navigation__primary-item {
    padding: var(--space-xs) var(--space-sm);
    min-height: 40px;
    min-width: 100px;
    flex: 0 1 auto;
  }

  .simplified-navigation__label {
    font-size: 0.8rem;
  }

  .simplified-navigation__description {
    display: none; /* Keep hidden on mobile */
  }

  .simplified-navigation__icon {
    width: 18px;
    height: 18px;
  }

  .simplified-navigation__secondary {
    left: var(--space-sm);
    right: var(--space-sm);
    transform: translateY(-50%);
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .simplified-navigation__container {
    gap: var(--space-xs);
    flex-direction: column;
    align-items: stretch;
  }

  .simplified-navigation__primary-item {
    min-height: 44px;
    min-width: auto;
    justify-content: center;
  }

  .simplified-navigation__description {
    display: none;
  }

  .simplified-navigation__secondary {
    top: 20%;
    bottom: 20%;
    left: var(--space-sm);
    right: var(--space-sm);
    transform: none;
    max-height: none;
    height: auto;
  }

  .simplified-navigation__secondary-content {
    max-height: none;
    height: calc(100% - 80px);
  }
}

/* Focus States for Accessibility */
.simplified-navigation__primary-item:focus,
.simplified-navigation__secondary-item:focus,
.simplified-navigation__close-btn:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

/* Kids Zone Special Styling */
.simplified-navigation__primary-item--special {
  position: relative;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF6347 100%);
  border: 2px solid #FFD700;
  color: #1A1A1A;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  animation: gentle-glow 2s ease-in-out infinite alternate;
}

.simplified-navigation__primary-item--special:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FF6347 50%, #FFD700 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.simplified-navigation__primary-item--special .simplified-navigation__icon {
  color: #1A1A1A;
}

.simplified-navigation__primary-item--special .simplified-navigation__label {
  color: #1A1A1A;
  font-weight: 700;
}

.simplified-navigation__primary-item--special .simplified-navigation__description {
  color: #2C2C2C;
  font-weight: 500;
}

.simplified-navigation__special-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 1.2rem;
  animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes gentle-glow {
  0% {
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  }
  100% {
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.5);
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
}

/* Visual separator before Kids Zone */
.simplified-navigation__primary-item--special::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: linear-gradient(to bottom, #FFD700, #FFA500);
  border-radius: 2px;
}

/* Mobile responsive adjustments for Kids Zone */
@media (max-width: 768px) {
  .simplified-navigation__primary-item--special {
    margin: 0.5rem 0;
    border-radius: 12px;
  }

  .simplified-navigation__special-indicator {
    top: -3px;
    right: -3px;
    font-size: 1rem;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .simplified-navigation__primary-item,
  .simplified-navigation__secondary-item,
  .simplified-navigation__close-btn {
    transition: none;
  }

  .simplified-navigation__secondary {
    animation: none;
  }

  .simplified-navigation__close-btn:hover {
    transform: none;
  }

  .simplified-navigation__primary-item--special {
    animation: none;
  }

  .simplified-navigation__special-indicator {
    animation: none;
  }
}
