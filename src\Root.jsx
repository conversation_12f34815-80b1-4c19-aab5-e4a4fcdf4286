// React import not needed with JSX transform
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import App from './App';
import Account from './components/Account';
import KidsMain from './components/kids/KidsMain';
import UXDemo from './components/UXDemo';
import ErrorBoundary from './components/ErrorBoundary';

const Root = () => (
  <ErrorBoundary>
    <Router>
      <Routes>
        <Route path="/" element={<App />} />
        <Route path="/account" element={<Account />} />
        <Route path="/kids/*" element={<KidsMain />} />
        <Route path="/demo" element={<UXDemo />} />
      </Routes>
    </Router>
  </ErrorBoundary>
);

export default Root;
