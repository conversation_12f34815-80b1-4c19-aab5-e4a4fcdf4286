/* NAROOP Icon System - Black & Gold Theme */

.naroop-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* Icon Sizes */
.naroop-icon--small {
  width: 16px;
  height: 16px;
}

.naroop-icon--medium {
  width: 24px;
  height: 24px;
}

.naroop-icon--large {
  width: 32px;
  height: 32px;
}

.naroop-icon--xlarge {
  width: 48px;
  height: 48px;
}

/* Icon Variants */
.naroop-icon--outline {
  stroke-width: 2;
  fill: none;
  stroke: currentColor;
}

.naroop-icon--filled {
  stroke-width: 0;
  fill: currentColor;
  stroke: none;
}

.naroop-icon--mixed {
  stroke-width: 2;
  fill: none;
  stroke: currentColor;
}

.naroop-icon--mixed circle[fill="currentColor"],
.naroop-icon--mixed polygon[fill="currentColor"],
.naroop-icon--mixed path[fill="currentColor"] {
  fill: currentColor;
}

/* Icon Colors */
.naroop-icon--current {
  color: currentColor;
}

.naroop-icon--primary {
  color: var(--color-heritage-gold);
}

.naroop-icon--secondary {
  color: var(--color-heritage-forest);
}

.naroop-icon--accent {
  color: var(--color-empowerment-amber);
}

.naroop-icon--heritage {
  color: var(--color-heritage-black);
}

.naroop-icon--success {
  color: var(--color-heritage-forest);
}

.naroop-icon--warning {
  color: var(--color-empowerment-amber);
}

.naroop-icon--danger {
  color: var(--color-heritage-burgundy);
}

.naroop-icon--muted {
  color: var(--color-heritage-forest);
  opacity: 0.7;
}

/* Interactive Icons */
.naroop-icon--interactive {
  cursor: pointer;
  transition: all 0.3s ease;
}

.naroop-icon--interactive:hover {
  transform: scale(1.1);
  color: var(--color-heritage-gold);
}

.naroop-icon--interactive:active {
  transform: scale(0.95);
}

/* Icon in Buttons */
.naroop-icon--in-button {
  margin-right: var(--space-xs);
}

.naroop-icon--in-button:last-child {
  margin-right: 0;
  margin-left: var(--space-xs);
}

.naroop-icon--in-button:only-child {
  margin: 0;
}

/* Icon Backgrounds - Pill-shaped design */
.naroop-icon--with-background {
  padding: var(--space-sm);
  border-radius: 50px; /* Pill-shaped instead of circular */
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-gold);
  min-width: 44px; /* Ensure proper touch target */
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.naroop-icon--with-background.naroop-icon--primary {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  border-color: var(--color-heritage-black);
}

.naroop-icon--with-background.naroop-icon--secondary {
  background: var(--color-heritage-forest);
  color: var(--color-heritage-cream);
  border-color: var(--color-heritage-cream);
}

.naroop-icon--with-background.naroop-icon--accent {
  background: var(--color-empowerment-amber);
  color: var(--color-heritage-black);
  border-color: var(--color-heritage-black);
}

/* Icon States */
.naroop-icon--loading {
  animation: iconSpin 1s linear infinite;
}

.naroop-icon--pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

.naroop-icon--bounce {
  animation: iconBounce 1s ease-in-out infinite;
}

/* Animations */
@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Icon Groups */
.naroop-icon-group {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.naroop-icon-group--vertical {
  flex-direction: column;
}

.naroop-icon-group--spaced {
  gap: var(--space-sm);
}

/* Cultural Heritage Styling */
.naroop-icon--heritage-style {
  filter: drop-shadow(0 2px 4px rgba(26, 26, 26, 0.2));
}

.naroop-icon--heritage-style.naroop-icon--primary {
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

/* Accessibility */
.naroop-icon[aria-hidden="true"] {
  pointer-events: none;
}

.naroop-icon:focus {
  outline: 2px solid var(--color-heritage-gold);
  outline-offset: 2px;
  border-radius: 4px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .naroop-icon {
    stroke-width: 3;
  }
  
  .naroop-icon--with-background {
    border-width: 3px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .naroop-icon--interactive:hover {
    transform: none;
  }
  
  .naroop-icon--loading,
  .naroop-icon--pulse,
  .naroop-icon--bounce {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .naroop-icon {
    color: black !important;
    background: white !important;
    border-color: black !important;
  }
  
  .naroop-icon--with-background {
    background: white !important;
    border: 1px solid black !important;
  }
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
  .naroop-icon--with-background {
    background: var(--color-heritage-black);
    color: var(--color-heritage-gold);
    border-color: var(--color-heritage-gold);
  }
}

/* Icon Specific Styles */
.naroop-icon[data-icon="heart"] {
  color: var(--color-heritage-burgundy);
}

.naroop-icon[data-icon="heart"].naroop-icon--active {
  fill: var(--color-heritage-burgundy);
  animation: iconPulse 0.6s ease-in-out;
}

.naroop-icon[data-icon="notification"] {
  position: relative;
}

.naroop-icon[data-icon="notification"]::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px; /* Slightly larger for better visibility */
  height: 8px;
  background: var(--color-heritage-burgundy);
  border-radius: 50px; /* Pill-shaped indicator */
  border: 2px solid var(--color-heritage-cream);
}

.naroop-icon[data-icon="excellence"] {
  color: var(--color-heritage-gold);
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.5));
}

.naroop-icon[data-icon="growth"] {
  color: var(--color-heritage-forest);
}

.naroop-icon[data-icon="celebration"] {
  color: var(--color-empowerment-amber);
}

.naroop-icon[data-icon="heritage"] {
  background: linear-gradient(45deg, var(--color-heritage-gold), var(--color-empowerment-amber));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .naroop-icon--interactive {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .naroop-icon--small {
    width: 20px;
    height: 20px;
  }
  
  .naroop-icon--medium {
    width: 28px;
    height: 28px;
  }
}
