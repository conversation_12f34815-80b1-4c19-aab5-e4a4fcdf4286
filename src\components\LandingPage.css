/* Landing Page Styles */
:root {
  /* NAROOP Black & Gold Color Palette */

  /* Primary Foundation Colors */
  --color-primary-cream: #FFF8DC; /* Warm cream background for all sections */
  --color-text-primary: #1A1A1A; /* Rich black for headlines and primary text */
  --color-text-secondary: #355E3B; /* Forest green for secondary text and accents */
  --color-accent-highlight: #FFD700; /* Classic gold for highlights and primary button hovers */

  /* Button and Interactive Element Colors */
  --color-button-bg-default: rgba(26, 26, 26, 0.1); /* Translucent black for default button backgrounds */
  --color-button-border: #1A1A1A; /* Rich black for button borders */
  --color-button-hover-primary: #FFD700; /* Classic gold fill for primary action buttons on hover */
  --color-button-hover-secondary: #8B6914; /* Darker deep gold fill for secondary buttons on hover */

  /* Card and Container Colors */
  --color-card-bg: #FFFFFF; /* White background for content cards */
  --color-card-shadow: rgb(26 26 26 / 0.1); /* Subtle black shadow for cards */

  /* Spacing and Layout - Enhanced for better visual hierarchy */
  --space-xs: 0.5rem;
  --space-sm: 0.75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 5rem;
  --space-5xl: 6rem;
  --space-6xl: 8rem;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease;
}

/* Global Button Pill Shape - Applied to all buttons throughout NAROOP */
button:not(.custom-shape) {
  border-radius: 9999px !important; /* Perfect pill shape for all buttons */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all var(--transition-normal) !important;
  font-weight: 600 !important;
  border: 2px solid var(--color-text-secondary) !important;
  min-height: 38px !important; /* Reduced from 44px to 38px */
  padding: 8px 16px !important; /* Reduced from 12px 24px */
  background-color: transparent !important;
  color: var(--color-text-secondary) !important;
  font-size: var(--text-sm) !important; /* Smaller default font size */
}

/* Small button variant */
button.btn-small:not(.custom-shape) {
  padding: 6px 12px !important; /* Reduced from 8px 16px */
  min-height: 28px !important; /* Reduced from 32px */
  font-size: 0.8rem !important; /* Slightly smaller */
}

/* Large button variant */
button.btn-large:not(.custom-shape) {
  padding: 12px 24px !important; /* Reduced from 20px 40px */
  min-height: 48px !important; /* Reduced from 60px */
  font-size: 1rem !important; /* Reduced from 1.2rem */
}

.landing-page {
  min-height: 100vh;
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  overflow-x: hidden;
  position: relative;
}

/* Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(253, 251, 245, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(89, 28, 40, 0.15);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md) var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header-logo .logo-image {
  height: 40px;
  width: 40px;
  object-fit: contain;
}

.header-logo .logo-text {
  font-size: var(--text-xl);
  font-weight: 900;
  color: var(--color-text-primary);
  text-shadow: none;
  letter-spacing: -0.02em;
}

.header-signin {
  background: transparent;
  border: 2px solid var(--color-text-secondary);
  color: var(--color-text-secondary);
  padding: 8px 16px; /* Reduced from 12px 24px */
  border-radius: 9999px; /* Perfect pill shape */
  font-weight: 700;
  font-size: var(--text-sm); /* Reduced from text-base */
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(110, 140, 101, 0.15);
  min-height: 36px; /* Reduced from 44px */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.header-signin:hover {
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.35);
  border-color: var(--color-accent-highlight);
}

/* Section spacing and transitions - Enhanced */
.landing-page section {
  position: relative;
}

.landing-page section:not(:last-child) {
  margin-bottom: var(--space-3xl); /* Reduced from 6rem to 4rem */
}

/* Hero Section - Better centered and spaced */
.landing-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--space-4xl) + 80px) var(--space-2xl) var(--space-4xl);
  position: relative;
  background: var(--color-primary-cream);
}

/* Professional section spacing and separators - Enhanced */
.landing-page section:not(.landing-hero):not(:last-child) {
  margin-bottom: var(--space-4xl); /* Reduced from 8rem to 5rem */
  padding-bottom: var(--space-2xl); /* Reduced from 4rem to 3rem */
}

/* Professional minimalistic section separators - Enhanced */
.landing-page section:not(.landing-hero):not(.landing-footer):not(.landing-footer-simplified)::after {
  content: '';
  position: absolute;
  bottom: var(--space-lg); /* Reduced from 2rem to 1.5rem */
  left: 50%;
  transform: translateX(-50%);
  width: 80px; /* Reduced from 120px to 80px for less visual weight */
  height: 1px; /* Reduced from 2px to 1px for subtlety */
  background: linear-gradient(90deg, transparent, rgba(89, 28, 40, 0.2), transparent); /* Reduced opacity */
}

/* Remove hero background overlay for clean design */

/* Hero content - Better spacing and centering */
.hero-content {
  max-width: 1400px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  margin: 0 auto;
}

.hero-text {
  z-index: 2;
  text-align: left;
}

.hero-title {
  margin: 0 0 var(--space-2xl) 0;
}

.brand-name {
  display: block;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  text-shadow: none;
  line-height: 0.9;
}

.brand-subtitle {
  display: block;
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 500;
  color: var(--color-text-secondary);
  text-shadow: none;
  font-style: italic;
  line-height: 1.2;
}

.hero-description {
  font-size: var(--text-lg);
  line-height: 1.8;
  margin: 0 0 var(--space-3xl) 0;
  color: var(--color-text-secondary);
  text-shadow: none;
  max-width: 650px;
}

/* Hero actions removed - Sign In moved to header */

.cta-primary, .cta-secondary {
  padding: 10px 20px; /* Reduced from 16px 32px */
  border-radius: 9999px; /* Perfect pill shape */
  font-size: var(--text-base); /* Reduced from text-lg */
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 2px solid var(--color-text-secondary);
  min-height: 40px; /* Reduced from 52px */
  min-width: 120px; /* Reduced from 160px */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: var(--color-text-secondary);
}

.cta-primary {
  /* Primary action buttons - fill with warm yellow on hover */
  border: 2px solid var(--color-text-secondary);
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.35);
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
}

.cta-secondary {
  /* Secondary/informational buttons - fill with deep gold on hover */
  border: 2px solid var(--color-text-secondary);
}

.cta-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(184, 134, 11, 0.35);
  background: var(--color-button-hover-secondary);
  color: var(--color-primary-cream);
  border-color: var(--color-text-secondary);
}



.hero-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

.hero-visual::before {
  content: '';
  position: absolute;
  top: -2rem;
  right: -2rem;
  width: 80px;
  height: 80px;
  background: var(--color-accent-highlight);
  border-radius: 15px; /* Square with rounded corners */
  transform: rotate(45deg);
  opacity: 0.12;
  animation: float 6s ease-in-out infinite;
}

.hero-visual::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: -1rem;
  width: 60px;
  height: 60px;
  background: var(--color-text-secondary);
  border-radius: 50px; /* Pill-shaped narrow icon */
  opacity: 0.10;
  animation: float 4s ease-in-out infinite reverse;
}

.hero-icon {
  font-size: clamp(4rem, 10vw, 8rem);
  margin-bottom: var(--space-lg);
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
  transition: filter var(--transition-normal);
  cursor: default;
}

.hero-icon:hover {
  filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.5));
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-tagline {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: 2px;
  text-transform: uppercase;
  text-shadow: none;
  background: rgba(253, 251, 245, 0.9);
  padding: var(--space-sm) var(--space-md);
  border-radius: 9999px; /* Pill-shaped narrow element */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(89, 28, 40, 0.15);
}

/* Scroll Indicator - Enhanced spacing */
.scroll-indicator {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none !important;
  box-shadow: none !important;
}

.scroll-text {
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.6em 2em;
  border-radius: 9999px;
  border: 2px solid var(--color-button-border);
  box-shadow: 0 4px 16px rgba(89,28,40,0.12);
  margin-bottom: 0.2em;
  outline: none;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.scroll-chevron {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  box-shadow: none;
  margin-top: 0;
}

.scroll-chevron::after {
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  border-right: 3px solid var(--color-button-border);
  border-bottom: 3px solid var(--color-button-border);
  transform: rotate(45deg);
  margin: 0 auto;
}

/* Remove extra borders/shadows from scroll-indicator and children */
.scroll-indicator,
.scroll-indicator * {
  box-shadow: none !important;
  border-width: 0 !important;
  background: none !important;
}

.scroll-text {
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-button-border) !important;
  background: #fff !important;
  color: var(--color-text-secondary) !important;
}

.scroll-indicator:focus-visible .scroll-text {
  outline: 3px solid var(--color-accent-highlight);
  outline-offset: 2px;
}

/* Features Section - Enhanced spacing and centering */
.landing-features {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-3xl) var(--space-2xl); /* Reduced from 4xl */
  position: relative;
}

.landing-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-accent-highlight);
}

.features-container {
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
}

.features-container h2 {
  font-size: var(--text-3xl); /* Reduced from 4xl */
  font-weight: 700;
  margin: 0 0 var(--space-xl) 0; /* Reduced from 2xl */
  color: var(--color-text-primary);
  line-height: 1.2;
}

.features-container::after {
  content: '';
  display: block;
  width: 60px; /* Reduced from 80px */
  height: 3px; /* Reduced from 4px */
  background: var(--color-accent-highlight);
  margin: var(--space-lg) auto var(--space-2xl) auto; /* Reduced spacing */
  border-radius: 2px;
}

/* Feature cards - Enhanced spacing and layout */
.features-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xl);
}

.feature-card {
  background: var(--color-card-bg);
  padding: var(--space-lg); /* Reduced from 2xl */
  border-radius: 1rem; /* Reduced from 1.5rem */
  text-align: left;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 2px solid var(--color-button-border);
  box-shadow: 0 4px 16px rgba(89, 28, 40, 0.1); /* Reduced shadow */
  margin-bottom: var(--space-lg); /* Reduced from 2xl */
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg); /* Reduced from 2xl */
}

.feature-card:hover {
  transform: translateY(-4px); /* Reduced from -8px */
  box-shadow: 0 8px 24px rgba(89, 28, 40, 0.15); /* Reduced shadow */
}

.feature-icon {
  font-size: 3rem; /* Larger since no background circle */
  margin-bottom: 0;
  margin-right: var(--space-lg);
  color: var(--color-text-primary); /* Use primary text color for better visibility */
  background: none; /* Remove circular background */
  border-radius: 0; /* Remove border radius */
  border: none; /* Remove border */
  padding: 0; /* Remove padding */
  min-width: auto; /* Remove fixed width */
  min-height: auto; /* Remove fixed height */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none; /* Remove shadow */
  flex-shrink: 0;
}

.feature-card h3 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-md) 0;
  line-height: 1.3;
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
}

.feature-card p {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
  font-size: var(--text-base);
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

/* Responsive grid for features - Better spacing and compact layout */
@media (min-width: 700px) {
  .features-grid {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-xl); /* Reduced gap for better fit */
    align-items: stretch; /* Ensure all cards stretch to same height */
  }
  .feature-card {
    flex-direction: column;
    align-items: center; /* Center align all content including icons */
    min-width: 260px; /* Reduced from 300px */
    max-width: 300px; /* Reduced from 350px */
    flex: 1 1 240px; /* Reduced from 280px */
    text-align: center; /* Center text for uniformity */
    padding: var(--space-md); /* Reduced from lg */
    min-height: 280px; /* Reduced from 320px */
    /* Remove max-height restriction to allow content to expand */
    overflow: visible; /* Allow content to be visible */
    box-sizing: border-box; /* Include padding in size calculations */
  }
  .feature-icon {
    margin-right: 0;
    margin-bottom: var(--space-xs); /* Reduced margin */
    align-self: center; /* Ensure icon is centered */
    font-size: 2rem; /* Reduced from 2.5rem */
  }
  .feature-card h3 {
    text-align: center;
    margin-bottom: var(--space-xs); /* Smaller margin for better fit */
    font-size: var(--text-lg); /* Slightly smaller heading */
    line-height: 1.2; /* Tighter line height */
    hyphens: auto; /* Allow hyphenation for long words */
  }
  .feature-card p {
    text-align: center;
    font-size: var(--text-sm); /* Slightly smaller text for compactness */
    line-height: 1.4; /* Balanced line height */
    margin: 0;
    /* Remove line clamping to show all text */
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto; /* Allow hyphenation for long words */
  }
}

/* Medium screen (tablet) adjustments */
@media (min-width: 700px) and (max-width: 1199px) {
  .feature-card {
    max-width: 280px;
    padding: var(--space-md);
    min-height: 260px;
  }
  
  .story-card {
    padding: var(--space-md);
  }
  
  .cta-primary.large,
  .cta-secondary.large {
    padding: 12px 24px;
    font-size: var(--text-base);
    min-height: 44px;
    min-width: 160px;
  }
  
  /* Reduce section padding on medium screens */
  .landing-features,
  .landing-about,
  .landing-cta {
    padding: var(--space-2xl) var(--space-lg);
  }
}

/* Larger screen adjustments for feature cards */
@media (min-width: 1200px) {
  .feature-card {
    max-width: 320px; /* Reduced from 380px */
    padding: var(--space-lg); /* Reduced from xl lg */
  }
  
  .feature-card h3 {
    font-size: var(--text-lg); /* Reduced from xl */
  }
  
  .feature-card p {
    font-size: var(--text-sm); /* Reduced from base */
    line-height: 1.5; /* Slightly tighter */
  }
}

/* Launch Section */
.landing-launch {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
}

/* Professional section separator */
.landing-launch::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(89, 28, 40, 0.1);
}

.launch-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.launch-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
}

.launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-accent-highlight);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  opacity: 0.95;
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: var(--color-card-bg);
  padding: var(--space-md); /* More compact padding */
  border-radius: 1rem; /* Consistent card border radius */
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px var(--color-card-shadow), 0 2px 4px -2px var(--color-card-shadow);
  box-sizing: border-box; /* Include padding in size calculations */
  overflow: hidden; /* Prevent content overflow */
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-base); /* Slightly smaller for better fit */
  font-weight: 600;
  color: var(--color-accent-highlight);
  line-height: 1.2; /* Tighter line height */
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.4; /* Balanced line height */
  font-size: var(--text-sm); /* Smaller text for better fit */
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
}

.stats-note {
  background: var(--color-card-bg);
  padding: var(--space-lg);
  border-radius: 1rem; /* Consistent card border radius */
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--color-accent-highlight);
  box-shadow: 0 4px 6px -1px var(--color-card-shadow), 0 2px 4px -2px var(--color-card-shadow);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: var(--color-text-secondary);
}

/* About Us Section - Enhanced spacing and centering */
.landing-about {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-3xl) var(--space-2xl); /* Reduced from 4xl */
  position: relative;
  overflow: hidden;
}

/* Professional section separator */
.landing-about::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(110, 140, 101, 0.2);
  pointer-events: none;
  z-index: 1;
}

.about-container {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.about-header {
  text-align: center;
  margin-bottom: var(--space-2xl); /* Reduced from 4xl */
}

.about-header h2 {
  font-size: var(--text-3xl); /* Reduced from 4xl */
  font-weight: 800;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-md) 0; /* Reduced from lg */
  line-height: 1.2;
}

.about-subtitle {
  font-size: var(--text-xl);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1.4;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-2xl); /* Reduced from 4xl */
  margin-bottom: var(--space-2xl); /* Reduced from 4xl */
}

/* Story cards - Enhanced spacing and visual hierarchy */
.founder-story {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-2xl);
  align-items: stretch; /* Ensure all cards stretch to accommodate content */
}

/* Responsive layout for story cards */
@media (min-width: 900px) {
  .founder-story {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-xl);
  }
}

@media (min-width: 600px) and (max-width: 899px) {
  .founder-story {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }
}

.story-card {
  background: var(--color-card-bg);
  border-radius: 1rem; /* Reduced from 1.5rem */
  padding: var(--space-md); /* Reduced from lg */
  box-shadow: 0 4px 20px rgba(89, 28, 40, 0.1); /* Reduced shadow */
  transition: all var(--transition-normal);
  border: 2px solid rgba(89, 28, 40, 0.1);
  position: relative;
  overflow: visible; /* Allow content to be visible */
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
  text-align: center; /* Center all content in story cards */
  min-height: auto; /* Allow natural height based on content */
  /* Remove max-height restriction to allow content to expand */
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* Align content to top instead of center */
  box-sizing: border-box; /* Include padding in size calculations */
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--color-text-secondary);
}

.story-card:hover {
  transform: translateY(-3px); /* Reduced from -6px */
  box-shadow: 0 8px 28px rgba(89, 28, 40, 0.15); /* Reduced shadow */
  border-color: var(--color-text-primary);
}

.story-card:nth-child(1) {
  animation-delay: 0.1s;
}

.story-card:nth-child(2) {
  animation-delay: 0.2s;
}

.story-card:nth-child(3) {
  animation-delay: 0.3s;
}

.story-icon {
  font-size: var(--text-3xl); /* Smaller icon for better fit */
  margin-bottom: var(--space-sm); /* Smaller margin for compactness */
  display: block;
  text-align: center; /* Center the icon */
  width: 100%;
}

.story-card h3 {
  font-size: var(--text-lg); /* Smaller for better fit */
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-sm) 0; /* Smaller margin */
  line-height: 1.2; /* Tighter line height */
  text-align: center; /* Center the heading */
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
  hyphens: auto; /* Allow hyphenation for long words */
}

.story-card p {
  font-size: var(--text-sm); /* Smaller text for better fit */
  line-height: 1.4; /* Balanced line height */
  color: var(--color-text-secondary);
  margin: 0;
  text-align: center; /* Center the text */
  word-wrap: break-word; /* Ensure text wraps within container */
  overflow-wrap: break-word; /* Modern property for text wrapping */
  hyphens: auto; /* Allow hyphenation for long words */
  /* Remove line clamping to show all text */
}

/* Mission statement - Enhanced spacing and centering */
.mission-statement {
  background: var(--color-card-bg);
  border-radius: 1.5rem;
  border: 3px solid var(--color-text-secondary);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.mission-card {
  padding: var(--space-4xl);
  color: var(--color-text-primary);
  text-align: center;
}

.mission-card h3 {
  font-size: var(--text-3xl);
  font-weight: 800;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.mission-card p {
  font-size: var(--text-xl);
  line-height: 1.7;
  margin: 0 0 var(--space-3xl) 0;
  color: var(--color-text-secondary);
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mission-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
  margin-top: var(--space-3xl);
}

.value-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm); /* Smaller gap for better fit */
  padding: var(--space-md) var(--space-lg); /* More compact padding */
  background: var(--color-card-bg);
  border-radius: 50px;
  border: 2px solid var(--color-text-secondary);
  transition: all var(--transition-normal);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.12);
  box-sizing: border-box; /* Include padding in size calculations */
  overflow: hidden; /* Prevent content overflow */
  min-height: 50px; /* Consistent minimum height */
  font-size: var(--text-sm); /* Smaller text for better fit */
  text-align: center; /* Center text */
  justify-content: center; /* Center content horizontally */
}

.value-item:hover {
  background: var(--color-card-bg);
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(110, 140, 101, 0.2);
  border-color: var(--color-accent-highlight);
}

.value-icon {
  font-size: var(--text-lg); /* Slightly smaller icon */
  color: var(--color-text-secondary);
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.about-closing {
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  border: 3px solid var(--color-text-primary);
  padding: var(--space-4xl);
  border-radius: 1.5rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  animation: fadeInUp 1s ease-out 0.6s both;
}

.about-closing h3 {
  font-size: var(--text-3xl);
  font-weight: 800;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.about-closing p {
  font-size: var(--text-xl);
  line-height: 1.7;
  margin: 0;
  color: var(--color-text-secondary);
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* CTA Section - Enhanced spacing and centering */
.landing-cta {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-3xl) var(--space-2xl); /* Reduced from 5xl */
  text-align: center;
  position: relative;
}

/* Professional section separator */
.landing-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(89, 28, 40, 0.1);
  pointer-events: none;
  z-index: 1;
}

.cta-container {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-container h2 {
  font-size: var(--text-3xl); /* Reduced from 4xl */
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0; /* Reduced from 2xl */
  line-height: 1.2;
}

.cta-container p {
  font-size: var(--text-lg); /* Reduced from xl */
  line-height: 1.7;
  margin: 0 0 var(--space-2xl) 0; /* Reduced from 4xl */
  opacity: 0.95;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg); /* Reduced from 2xl */
}

/* Enhanced CTA buttons with better spacing */
.cta-primary.large {
  padding: 14px 28px; /* Reduced from 24px 48px */
  font-size: var(--text-lg); /* Reduced from text-2xl */
  background: transparent;
  color: var(--color-text-secondary);
  box-shadow: 0 8px 24px rgba(110, 140, 101, 0.15); /* Reduced shadow */
  font-weight: 700; /* Reduced from 800 */
  min-height: 48px; /* Reduced from 70px */
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.5px; /* Reduced from 1px */
  border: 2px solid var(--color-text-secondary); /* Reduced from 3px */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px; /* Reduced from 250px */
}

.cta-primary.large:hover {
  transform: translateY(-3px); /* Reduced from -6px */
  box-shadow: 0 12px 32px rgba(247, 208, 70, 0.35); /* Reduced shadow */
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
}

.cta-secondary.large {
  padding: 14px 28px; /* Reduced from 24px 48px */
  font-size: var(--text-lg); /* Reduced from text-2xl */
  background: transparent;
  color: var(--color-text-secondary);
  border: 2px solid var(--color-text-secondary); /* Reduced from 3px */
  box-shadow: 0 8px 24px rgba(110, 140, 101, 0.15); /* Reduced shadow */
  font-weight: 700; /* Reduced from 800 */
  min-height: 48px; /* Reduced from 70px */
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.5px; /* Reduced from 1px */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px; /* Reduced from 250px */
}

.cta-secondary.large:hover {
  transform: translateY(-3px); /* Reduced from -6px */
  background: var(--color-button-hover-secondary);
  color: var(--color-primary-cream);
  border-color: var(--color-text-secondary);
  box-shadow: 0 12px 32px rgba(110, 140, 101, 0.35); /* Reduced shadow */
}

.cta-note {
  margin: var(--space-lg) 0 0 0;
  color: var(--color-text-secondary);
  font-size: var(--text-lg);
}

.guest-note {
  margin-top: var(--space-xl);
  padding: var(--space-lg) var(--space-xl);
  background: transparent;
  border-radius: 9999px;
  border-left: 4px solid var(--color-accent-highlight);
  color: var(--color-text-secondary);
  font-size: var(--text-base);
  line-height: 1.6;
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.guest-note strong {
  color: var(--color-accent-highlight);
}

.link-button {
  background: transparent;
  border: 2px solid var(--color-text-secondary);
  color: var(--color-text-secondary);
  text-decoration: none;
  cursor: pointer;
  font-size: inherit;
  font-weight: 600;
  margin-left: var(--space-xs);
  transition: all var(--transition-normal);
  text-shadow: none;
  padding: 8px 16px; /* Fixed padding for perfect small pill shape */
  border-radius: 9999px; /* Perfect pill shape */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px; /* Optimal height for small pill shape */
}

.link-button:hover {
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.25);
}

/* Landing Footer - Simplified Design */
.landing-footer-simplified {
  background: #FDFBF5; /* Light cream background */
  color: #591C28; /* Dark maroon text */
  border-top: 2px solid #6E8C65; /* Muted green accent border */
  padding: var(--space-3xl) var(--space-2xl) var(--space-2xl) var(--space-2xl);
  position: relative;
}

/* Use the same styles as the main footer component */
.landing-footer-simplified .footer-container,
.landing-footer-simplified .footer-main,
.landing-footer-simplified .footer-brand,
.landing-footer-simplified .footer-brand-title,
.landing-footer-simplified .footer-brand-subtitle,
.landing-footer-simplified .footer-mission,
.landing-footer-simplified .footer-links-section,
.landing-footer-simplified .footer-links-group,
.landing-footer-simplified .footer-links-title,
.landing-footer-simplified .footer-links,
.landing-footer-simplified .footer-link,
.landing-footer-simplified .footer-bottom,
.landing-footer-simplified .footer-bottom-content,
.landing-footer-simplified .footer-copyright,
.landing-footer-simplified .footer-tagline,
.landing-footer-simplified .footer-version,
.landing-footer-simplified .version-badge {
  /* Inherit all styles from the main footer component */
}

/* Landing page specific footer adjustments */
.landing-footer-simplified {
  margin-top: var(--space-3xl);
}

.landing-footer-simplified .footer-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* === ENHANCEMENTS FOR CONSISTENCY, OUTLINES, AND ACCESSIBILITY === */

/* Stronger outlines for all cards and pill-shaped elements - Black & Gold Theme */
.feature-card,
.story-card,
.mission-statement,
.about-closing,
.value-item,
.cta-primary,
.cta-secondary,
.link-button,
.scroll-text,
.guest-note,
.version-info {
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-text-secondary, #B87333) !important;
  box-shadow: 0 2px 12px rgba(26, 26, 26, 0.08), 0 1.5px 6px rgba(53, 94, 59, 0.08);
}

/* Use forest green for secondary outlines and accents */
.value-item,
.guest-note {
  border-color: var(--color-text-secondary, #355E3B) !important;
}

/* Gold styling for all clickable buttons */
.cta-primary,
.cta-secondary,
.link-button,
.header-signin {
  border-color: var(--color-accent-highlight, #FFD700) !important;
  color: var(--color-accent-highlight, #FFD700) !important;
  background: transparent !important;
}

/* Add subtle gold or black left border for feature and story cards */
.feature-card {
  border-left: 5px solid var(--color-accent-highlight, #FFD700) !important;
}
.story-card {
  border-left: 5px solid var(--color-button-border, #1A1A1A) !important;
}

/* Pill-shaped icons and taglines: add clear outlines and background */
.hero-tagline,
.scroll-text {
  border: 2px solid var(--color-button-border, #1A1A1A) !important;
  background: rgba(255, 248, 220, 0.97) !important;
}

/* Feature and story icons: remove circle background and outline */
.feature-icon,
.story-icon {
  background: none; /* Remove background */
  border-radius: 0; /* Remove border radius */
  border: none; /* Remove border */
  padding: 0; /* Remove padding */
  margin-bottom: var(--space-md);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: none; /* Remove shadow */
}

/* Mission values: green outline and consistent background */
.value-item {
  border: 2px solid var(--color-text-secondary, #6E8C65) !important;
  background: var(--color-card-bg) !important;
}
.value-item:hover {
  background: var(--color-card-bg) !important;
  border-color: var(--color-accent-highlight, #F7D046) !important;
}

/* Focus-visible for accessibility */
button:focus-visible,
.link-button:focus-visible,
.scroll-indicator:focus-visible {
  outline: 3px solid var(--color-accent-highlight, #F7D046) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(247, 208, 70, 0.15) !important;
}

/* Ensure text contrast on all backgrounds */
.mission-statement,
.landing-footer {
  color: var(--color-text-primary) !important;
}
.mission-card h3,
.about-closing h3 {
  color: var(--color-text-primary) !important;
}

/* Footer links: consistent color scheme */
.footer-links a:hover {
  color: var(--color-text-primary, #591C28) !important;
}

/* Add a subtle green shadow to CTA secondary on hover */
.cta-secondary.large:hover {
  box-shadow: 0 15px 40px rgba(110, 140, 101, 0.35) !important;
}

/* --- Scroll Indicator: Remove all container borders and box-shadows --- */
.scroll-indicator {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: none !important;
}

/* Remove border from .scroll-indicator and its direct children except .scroll-text and .scroll-chevron::after */
.scroll-indicator > *:not(.scroll-text):not(.scroll-chevron) {
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* Remove border from .scroll-chevron itself (the arrow is in ::after) */
.scroll-chevron {
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* Remove any global border rules for .scroll-indicator from the enhancements section */
.feature-card,
.story-card,
.mission-statement,
.about-closing,
.value-item,
.scroll-text,
.guest-note,
.version-info {
  /* .scroll-indicator and buttons removed from this list to prevent border override */
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-text-secondary, #6E8C65) !important;
  box-shadow: 0 2px 12px rgba(89, 28, 40, 0.08), 0 1.5px 6px rgba(110, 140, 101, 0.08);
}

/* Utility classes for content overflow control */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Note: Removed line-clamp utilities to ensure all text is visible */

/* Ensure all card containers have proper box-sizing */
.feature-card,
.story-card,
.launch-feature,
.value-item,
.mission-card,
.about-closing {
  box-sizing: border-box;
  /* Remove overflow hidden to allow all content to be visible */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Responsive Design - Enhanced spacing for mobile */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-3xl);
    padding: 0 var(--space-lg);
  }

  .hero-text {
    text-align: center;
  }

  .hero-actions {
    justify-content: center;
  }

  /* Override the single button centering for mobile */
  .hero-actions:has(.cta-secondary:only-child) {
    justify-content: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 350px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .feature-card {
    flex-direction: column;
    align-items: center; /* Center align on mobile too */
    text-align: center; /* Center text on mobile */
    padding: var(--space-lg); /* More compact padding on mobile */
    min-height: auto; /* Allow natural height on mobile */
    max-height: none; /* Remove height restriction on mobile */
  }

  .feature-icon {
    margin-right: 0;
    margin-bottom: var(--space-md); /* Reduced margin on mobile */
    align-self: center;
  }

  .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-3xl);
  }

  .launch-message {
    text-align: center;
  }

  .landing-footer-simplified .footer-main {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    text-align: center;
  }

  .landing-footer-simplified .footer-links-section {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .landing-footer-simplified .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  /* Enhanced mobile spacing */
  .landing-hero {
    padding: calc(var(--space-3xl) + 80px) var(--space-lg) var(--space-3xl);
  }

  .landing-features,
  .landing-about,
  .landing-cta {
    padding: var(--space-3xl) var(--space-lg);
  }

  .about-header {
    margin-bottom: var(--space-3xl);
  }

  .about-content {
    gap: var(--space-3xl);
    margin-bottom: var(--space-3xl);
  }

  .cta-actions {
    gap: var(--space-xl);
  }
}

/* Mobile - Extra small screens */
@media (max-width: 480px) {
  .landing-hero {
    padding: calc(var(--space-2xl) + 80px) var(--space-md) var(--space-2xl);
  }

  .landing-features,
  .landing-about,
  .landing-cta {
    padding: var(--space-2xl) var(--space-md);
  }

  .hero-content {
    gap: var(--space-2xl);
  }

  .brand-name {
    font-size: clamp(2.5rem, 10vw, 4rem);
  }

  .brand-subtitle {
    font-size: clamp(1rem, 4vw, 1.5rem);
  }

  .hero-description {
    font-size: var(--text-base);
    margin-bottom: var(--space-2xl);
  }

  .features-container h2,
  .about-header h2,
  .cta-container h2 {
    font-size: var(--text-2xl);
  }

  .features-container::after {
    margin: var(--space-lg) auto var(--space-2xl) auto;
  }

  .feature-card {
    padding: var(--space-md); /* More compact on small screens */
    gap: var(--space-md);
    text-align: center;
    align-items: center;
  }

  .feature-icon {
    min-width: 45px; /* Smaller icons on small screens */
    min-height: 45px;
    font-size: 1.8rem;
    align-self: center;
  }

  .story-card {
    padding: var(--space-lg);
    min-height: auto; /* Allow natural height on mobile */
    max-height: none; /* Remove height restriction on mobile */
    text-align: center; /* Ensure center alignment on mobile */
  }

  .story-card h3 {
    font-size: var(--text-lg);
    text-align: center;
  }

  .story-card p {
    font-size: var(--text-sm); /* Smaller text for mobile compactness */
    text-align: center;
  }

  .mission-card {
    padding: var(--space-2xl);
  }

  .mission-card h3 {
    font-size: var(--text-xl);
  }

  .mission-card p {
    font-size: var(--text-base);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .value-item {
    padding: var(--space-md);
    font-size: var(--text-sm);
  }

  .about-closing {
    padding: var(--space-2xl);
  }

  .about-closing h3 {
    font-size: var(--text-xl);
  }

  .about-closing p {
    font-size: var(--text-base);
  }

  .cta-container {
    max-width: 100%;
  }

  .cta-container p {
    font-size: var(--text-base);
  }

  .cta-primary.large,
  .cta-secondary.large {
    padding: 18px 36px;
    font-size: var(--text-lg);
    min-height: 60px;
    min-width: 200px;
  }

  .cta-actions {
    gap: var(--space-lg);
  }

  .guest-note {
    padding: var(--space-md);
    font-size: var(--text-sm);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .scroll-indicator {
    bottom: 1.5rem;
  }

  .scroll-text {
    font-size: 0.75rem;
    padding: 0.3em 1.2em;
  }

  .scroll-chevron {
    width: 20px;
    height: 20px;
  }

  .header-content {
    padding: var(--space-sm) var(--space-md);
  }

  .header-logo .logo-image {
    height: 32px;
    width: 32px;
  }

  .header-logo .logo-text {
    font-size: var(--text-lg);
  }

  .header-signin {
    padding: 8px 16px;
    font-size: 0.75rem;
    min-height: 36px;
  }
}

/* Tablet Styles for About Us - Enhanced spacing */
@media (min-width: 768px) {
  .about-content {
    grid-template-columns: 2fr 1fr;
    gap: var(--space-5xl);
    align-items: start;
  }

  .founder-story {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .value-item {
    justify-content: center;
    text-align: center;
    flex-direction: column;
    gap: var(--space-sm);
    padding: var(--space-xl);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }

  .about-subtitle {
    font-size: var(--text-xl);
  }

  .features-grid {
    justify-content: center;
  }
}

/* Desktop Styles for About Us - Enhanced spacing */
@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-4xl);
  }

  .founder-story {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2xl);
  }

  .mission-values {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-lg);
  }

  .value-item {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    gap: var(--space-md);
    padding: var(--space-lg);
  }

  .story-card {
    padding: var(--space-2xl); /* Keep compact padding on desktop */
    min-height: 280px; /* Consistent height on desktop */
    max-height: 320px;
  }

  .mission-card {
    padding: var(--space-4xl);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }

  .about-closing {
    padding: var(--space-4xl);
  }
}

/* Mobile Styles for About Us - Enhanced for better spacing */
@media (max-width: 480px) {
  .landing-about {
    padding: var(--space-2xl) var(--space-md);
  }

  .about-header {
    margin-bottom: var(--space-2xl);
  }

  .about-header h2 {
    font-size: var(--text-2xl);
  }

  .about-subtitle {
    font-size: var(--text-base);
  }

  .about-content {
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
  }

  .founder-story {
    gap: var(--space-xl);
  }

  .story-card {
    padding: var(--space-md); /* Even more compact on small screens */
    min-height: auto;
    max-height: none;
    text-align: center;
  }

  .story-card h3 {
    font-size: var(--text-base); /* Smaller heading on small screens */
    text-align: center;
  }

  .story-card p {
    font-size: var(--text-sm);
    text-align: center;
  }

  .mission-card {
    padding: var(--space-lg);
  }

  .mission-card h3 {
    font-size: var(--text-xl);
  }

  .mission-card p {
    font-size: var(--text-base);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }

  .value-item {
    padding: var(--space-sm);
    font-size: var(--text-sm);
  }

  .about-closing {
    padding: var(--space-lg);
  }

  .about-closing h3 {
    font-size: var(--text-xl);
  }

  .about-closing p {
    font-size: var(--text-base);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .landing-page {
    background: #000;
    color: #fff;
  }

  .landing-features {
    background: #fff;
    color: #000;
  }

  .feature-card {
    background: #f0f0f0;
    border: 2px solid #000;
  }

  .cta-primary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }

  .cta-secondary {
    background: transparent;
    border: 2px solid #fff;
  }

  .landing-about {
    background: #fff;
    color: #000;
  }

  .story-card {
    background: #f0f0f0;
    border: 2px solid #000;
    color: #000;
  }

  .mission-statement {
    background: #000;
    color: #fff;
  }

  .mission-card {
    color: #fff;
  }

  .value-item {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid #000;
    color: #000;
  }

  .about-closing {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
}

/* === AUTHENTICATION MODALS === */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px); /* Safari support */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-lg);
  animation: fadeIn 0.3s ease-out;
}

.auth-modal-content {
  background: var(--color-card-bg);
  border-radius: 1rem;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 2px solid var(--color-text-secondary);
  position: relative;
  max-width: 560px;
  width: 100%;
  min-height: auto;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Hide sign-in link/button in signup modal - More specific targeting */
.auth-modal-content .auth-form p:last-child {
  display: none !important;
}

/* Also hide any auth-links sections in signup modal */
.auth-modal-content .auth-links {
  display: none !important;
}

/* Modal close button */
.auth-modal-close {
  position: absolute !important;
  top: var(--space-md) !important;
  right: var(--space-md) !important;
  background: transparent !important;
  border: none !important;
  font-size: 1.5rem !important;
  color: var(--color-text-secondary) !important;
  cursor: pointer !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  transition: all var(--transition-normal) !important;
  z-index: 10 !important;
  padding: 0 !important;
  margin: 0 !important;
  min-height: auto !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  font-weight: normal !important;
  box-shadow: none !important;
}

.auth-modal-close:hover {
  background: rgba(89, 28, 40, 0.1) !important;
  color: var(--color-text-primary) !important;
  transform: rotate(90deg) !important;
  border: none !important;
  box-shadow: none !important;
}

/* Hide scrollbars in modal */
.auth-modal-content::-webkit-scrollbar {
  display: none;
}
.auth-modal-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Remove the back button from auth forms when in modal */
.auth-modal-overlay .auth-modal-content .back-to-landing,
.auth-modal-content .auth-form .back-to-landing {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Override auth form styling in modal */
.auth-modal-content .auth-form {
  background: var(--color-card-bg) !important;
  backdrop-filter: none !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 1rem !important;
  margin: 0 !important;
  padding: var(--space-lg) var(--space-xl) var(--space-xl) var(--space-xl) !important;
  padding-top: calc(var(--space-lg) + 20px) !important;
  max-width: none !important;
  width: 100% !important;
  box-sizing: border-box !important;
  min-height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
}

/* Ensure auth brand section looks good in modal */
.auth-modal-content .auth-brand {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.auth-modal-content .auth-logo {
  width: 60px !important;
  height: 60px !important;
  margin-bottom: var(--space-md) !important;
  filter: none !important;
  animation: none !important;
}

.auth-modal-content .auth-brand h2 {
  color: var(--color-text-primary) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
}

.auth-modal-content .auth-subtitle {
  color: var(--color-text-secondary) !important;
  font-size: var(--text-base) !important;
  margin-bottom: 0 !important;
}

/* Style form inputs to match NAROOP design */
.auth-modal-content input {
  width: 100% !important;
  padding: var(--space-sm) var(--space-md) !important;
  border: 2px solid var(--color-text-secondary) !important;
  border-radius: 9999px !important;
  font-size: var(--text-sm) !important;
  background: var(--color-card-bg) !important;
  color: var(--color-text-primary) !important;
  margin-bottom: var(--space-xs) !important;
  transition: all var(--transition-normal) !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
  transform: none !important;
  font-weight: 400 !important;
  min-height: 42px !important;
  box-sizing: border-box !important;
}

.auth-modal-content input:focus {
  outline: none !important;
  border-color: var(--color-accent-highlight) !important;
  box-shadow: 0 0 0 3px rgba(247, 208, 70, 0.2) !important;
  background: var(--color-card-bg) !important;
  transform: none !important;
}

.auth-modal-content input::placeholder {
  color: var(--color-text-secondary) !important;
  opacity: 0.7 !important;
  font-weight: 400 !important;
}

/* Style textarea elements in modal */
.auth-modal-content textarea {
  width: 100% !important;
  padding: var(--space-sm) var(--space-md) !important;
  border: 2px solid var(--color-text-secondary) !important;
  border-radius: 1rem !important;
  font-size: var(--text-sm) !important;
  background: var(--color-card-bg) !important;
  color: var(--color-text-primary) !important;
  margin-bottom: var(--space-xs) !important;
  transition: all var(--transition-normal) !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
  transform: none !important;
  font-weight: 400 !important;
  min-height: 60px !important;
  resize: vertical !important;
  font-family: inherit !important;
  box-sizing: border-box !important;
}

.auth-modal-content textarea:focus {
  outline: none !important;
  border-color: var(--color-accent-highlight) !important;
  box-shadow: 0 0 0 3px rgba(247, 208, 70, 0.2) !important;
  background: var(--color-card-bg) !important;
  transform: none !important;
}

.auth-modal-content textarea::placeholder {
  color: var(--color-text-secondary) !important;
  opacity: 0.7 !important;
  font-weight: 400 !important;
}

/* Style form buttons to match NAROOP design */
.auth-modal-content button[type="submit"] {
  width: 100% !important;
  background: transparent !important;
  border: 2px solid var(--color-text-secondary) !important;
  color: var(--color-text-secondary) !important;
  padding: var(--space-sm) var(--space-lg) !important;
  border-radius: 9999px !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all var(--transition-normal) !important;
  margin: var(--space-md) 0 var(--space-sm) 0 !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: none !important;
  overflow: visible !important;
  position: relative !important;
}

.auth-modal-content button[type="submit"]:before {
  display: none !important;
}

.auth-modal-content button[type="submit"]:hover:not(:disabled) {
  background: var(--color-button-hover-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-accent-highlight) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.35) !important;
}

.auth-modal-content button[type="submit"]:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Style auth links */
.auth-modal-content .auth-links {
  text-align: center !important;
  margin-top: var(--space-lg) !important;
}

.auth-modal-content .auth-links p {
  color: var(--color-text-secondary) !important;
  margin-bottom: var(--space-sm) !important;
  font-size: var(--text-base) !important;
  font-weight: 400 !important;
}

.auth-modal-content .auth-links button {
  background: transparent !important;
  border: none !important;
  color: var(--color-text-primary) !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  text-decoration: underline !important;
  transition: color var(--transition-normal) !important;
  font-size: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  min-height: auto !important;
  display: inline !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  box-shadow: none !important;
}

.auth-modal-content .auth-links button:hover {
  color: var(--color-accent-highlight) !important;
  background: transparent !important;
  border: none !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Error messages */
.auth-modal-content .auth-error {
  background: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
  color: #dc3545 !important;
  padding: var(--space-md) !important;
  border-radius: 0.5rem !important;
  margin-bottom: var(--space-lg) !important;
  font-size: var(--text-sm) !important;
  text-align: center !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    transform: translateY(-30px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Mobile responsiveness */
@media (max-width: 699px) {
  .feature-card {
    padding: var(--space-lg) var(--space-md); /* More compact mobile padding */
    margin-bottom: var(--space-lg); /* Reduced margin */
    min-height: auto; /* Allow natural height on mobile */
    max-height: none; /* Remove height restrictions on mobile */
  }
  
  .feature-card h3 {
    font-size: var(--text-lg); /* Smaller heading on mobile */
    margin-bottom: var(--space-sm);
  }
  
  .feature-card p {
    font-size: var(--text-sm); /* Smaller text on mobile */
    line-height: 1.5;
  }
  
  .story-card {
    padding: var(--space-md); /* More compact mobile padding */
    min-height: auto; /* Allow natural height on mobile */
    max-height: none; /* Remove height restrictions on mobile */
  }
  
  .story-card h3 {
    font-size: var(--text-base); /* Smaller heading on mobile */
  }
  
  .story-card p {
    font-size: var(--text-sm); /* Smaller text on mobile */
    -webkit-line-clamp: none; /* Remove line clamp on mobile */
    line-clamp: none;
    display: block; /* Normal display on mobile */
  }
  
  .launch-feature {
    padding: var(--space-sm) var(--space-md); /* More compact mobile padding */
    flex-direction: column; /* Stack content vertically on mobile */
    text-align: center; /* Center align on mobile */
    gap: var(--space-sm);
  }
  
  .mission-values {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: var(--space-md);
  }
  
  .value-item {
    padding: var(--space-sm) var(--space-md); /* More compact mobile padding */
    font-size: var(--text-xs); /* Smaller text on mobile */
    min-height: 40px; /* Smaller minimum height on mobile */
  }
  
  
  .auth-modal-overlay {
    padding: var(--space-sm) !important;
    align-items: flex-start !important;
    padding-top: var(--space-lg) !important;
  }

  .auth-modal-content {
    max-width: 95vw !important;
    min-height: auto !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    margin-top: 0 !important;
  }
  
  .auth-modal-content .auth-form {
    padding: var(--space-lg) var(--space-md) var(--space-md) var(--space-md) !important;
    padding-top: calc(var(--space-lg) + 20px) !important;
  }
  
  .auth-modal-content input,
  .auth-modal-content textarea {
    padding: var(--space-sm) var(--space-md) !important;
    min-height: 44px !important;
    margin-bottom: var(--space-xs) !important;
    font-size: var(--text-sm) !important;
  }
  
  .auth-modal-content textarea {
    min-height: 70px !important;
  }
}
