import React from 'react';
import './ColorThemeDemo.css';

const ColorThemeDemo = () => {
  return (
    <div className="color-theme-demo">
      <header className="demo-header">
        <h1>NAROOP Black & Gold Theme</h1>
        <p>Culturally Authentic Colors for African American Community Empowerment</p>
      </header>

      {/* Color Palette Display */}
      <section className="color-palette-section">
        <h2>Color Palette</h2>
        <div className="color-grid">
          <div className="color-card">
            <div className="color-swatch heritage-black"></div>
            <h3>Heritage Black</h3>
            <p>#1A1A1A</p>
            <span>Strength & Heritage</span>
          </div>
          <div className="color-card">
            <div className="color-swatch heritage-gold"></div>
            <h3>Heritage Gold</h3>
            <p>#FFD700</p>
            <span>Excellence & Achievement</span>
          </div>
          <div className="color-card">
            <div className="color-swatch heritage-deep-gold"></div>
            <h3>Deep Gold</h3>
            <p>#8B6914</p>
            <span>Wisdom & Prosperity</span>
          </div>
          <div className="color-card">
            <div className="color-swatch heritage-cream"></div>
            <h3>Heritage Cream</h3>
            <p>#FFF8DC</p>
            <span>Light & Hope</span>
          </div>
          <div className="color-card">
            <div className="color-swatch empowerment-amber"></div>
            <h3>Empowerment Amber</h3>
            <p>#FFBF00</p>
            <span>Joy & Celebration</span>
          </div>
          <div className="color-card">
            <div className="color-swatch heritage-forest"></div>
            <h3>Heritage Forest</h3>
            <p>#355E3B</p>
            <span>Growth & Hope</span>
          </div>
        </div>
      </section>

      {/* Button Examples */}
      <section className="button-examples-section">
        <h2>Button Styles</h2>
        <div className="button-grid">
          <button className="btn-primary">Primary Action</button>
          <button className="btn-secondary">Secondary Action</button>
          <button className="btn-success">Success Action</button>
          <button className="btn-warning">Warning Action</button>
          <button className="btn-danger">Danger Action</button>
          <button className="btn-outline">Outline Button</button>
        </div>
      </section>

      {/* Typography Examples */}
      <section className="typography-section">
        <h2>Typography</h2>
        <div className="typography-examples">
          <h1 className="demo-h1">Heading 1 - Heritage Black</h1>
          <h2 className="demo-h2">Heading 2 - Heritage Gold</h2>
          <h3 className="demo-h3">Heading 3 - Deep Gold</h3>
          <p className="demo-paragraph">
            This is body text using our heritage black color. It maintains excellent readability 
            while honoring the cultural significance of our color palette. The contrast ratio 
            exceeds WCAG AA standards for accessibility.
          </p>
          <p className="demo-secondary-text">
            This is secondary text using forest green, representing growth and hope in the 
            African American community journey.
          </p>
        </div>
      </section>

      {/* Card Examples */}
      <section className="card-examples-section">
        <h2>Card Components</h2>
        <div className="card-grid">
          <div className="demo-card primary-card">
            <h3>Primary Card</h3>
            <p>Features heritage gold accents and maintains cultural authenticity.</p>
            <button className="card-btn">Learn More</button>
          </div>
          <div className="demo-card secondary-card">
            <h3>Secondary Card</h3>
            <p>Uses forest green for growth and community connection themes.</p>
            <button className="card-btn secondary">Explore</button>
          </div>
          <div className="demo-card accent-card">
            <h3>Accent Card</h3>
            <p>Empowerment amber for celebration and positive energy.</p>
            <button className="card-btn accent">Celebrate</button>
          </div>
        </div>
      </section>

      {/* Cultural Significance */}
      <section className="cultural-section">
        <h2>Cultural Significance</h2>
        <div className="significance-grid">
          <div className="significance-item">
            <div className="significance-icon alpha-phi-alpha">ΑΦΑ</div>
            <h3>Alpha Phi Alpha Heritage</h3>
            <p>
              Our black and gold palette honors Alpha Phi Alpha Fraternity, founded in 1906 
              as the first African American intercollegiate fraternity, representing excellence, 
              scholarship, and brotherhood.
            </p>
          </div>
          <div className="significance-item">
            <div className="significance-icon pan-african">🌍</div>
            <h3>Pan-African Symbolism</h3>
            <p>
              Black represents the people and their strength, while gold represents the wealth 
              of the African continent and the prosperity of its diaspora.
            </p>
          </div>
          <div className="significance-item">
            <div className="significance-icon empowerment">✊</div>
            <h3>Modern Empowerment</h3>
            <p>
              These colors embody the journey from struggle to triumph, representing resilience, 
              success, and the bright future of the African American community.
            </p>
          </div>
        </div>
      </section>

      {/* Accessibility Report */}
      <section className="accessibility-section">
        <h2>Accessibility Compliance</h2>
        <div className="accessibility-grid">
          <div className="accessibility-item">
            <div className="accessibility-score">100%</div>
            <h3>WCAG AA Compliance</h3>
            <p>All color combinations meet or exceed WCAG AA contrast requirements</p>
          </div>
          <div className="accessibility-item">
            <div className="accessibility-score">16.3:1</div>
            <h3>Primary Text Contrast</h3>
            <p>Black text on cream background exceeds minimum 4.5:1 requirement</p>
          </div>
          <div className="accessibility-item">
            <div className="accessibility-score">12.4:1</div>
            <h3>Gold on Black Contrast</h3>
            <p>Gold text on black background for excellent readability</p>
          </div>
        </div>
      </section>

      <footer className="demo-footer">
        <p>NAROOP - Narrative of Our People</p>
        <p>Empowering the African American community through authentic design</p>
      </footer>
    </div>
  );
};

export default ColorThemeDemo;
