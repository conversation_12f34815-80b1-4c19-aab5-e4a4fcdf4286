/* NAROOP Black & Gold Theme Demo Styles */

.color-theme-demo {
  background: linear-gradient(135deg, #FFF8DC 0%, #F7E7CE 100%);
  min-height: 100vh;
  padding: 2rem;
  font-family: system-ui, -apple-system, sans-serif;
  color: #1A1A1A;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #1A1A1A 0%, #8B6914 100%);
  color: #FFF8DC;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(26, 26, 26, 0.2);
}

.demo-header h1 {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-header p {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

/* Color Palette Section */
.color-palette-section {
  margin-bottom: 3rem;
}

.color-palette-section h2 {
  color: #1A1A1A;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.color-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(26, 26, 26, 0.1);
  border: 2px solid #355E3B;
  transition: transform 0.3s ease;
}

.color-card:hover {
  transform: translateY(-4px);
}

.color-swatch {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem auto;
  border: 3px solid #1A1A1A;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.heritage-black { background: #1A1A1A; }
.heritage-gold { background: #FFD700; }
.heritage-deep-gold { background: #8B6914; }
.heritage-cream { background: #FFF8DC; }
.empowerment-amber { background: #FFBF00; }
.heritage-forest { background: #355E3B; }

.color-card h3 {
  color: #1A1A1A;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.color-card p {
  font-family: monospace;
  color: #8B4513;
  margin: 0 0 0.5rem 0;
  font-weight: bold;
}

.color-card span {
  color: #355E3B;
  font-style: italic;
  font-size: 0.9rem;
}

/* Button Examples */
.button-examples-section {
  margin-bottom: 3rem;
}

.button-examples-section h2 {
  color: #1A1A1A;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.button-grid button {
  padding: 0.875rem 1.75rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  min-height: 44px;
}

.btn-primary {
  background: transparent;
  border-color: #FFD700;
  color: #FFD700;
}

.btn-primary:hover {
  background: #FFD700;
  color: #1A1A1A;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
}

.btn-secondary {
  background: transparent;
  border-color: #8B6914;
  color: #8B6914;
}

.btn-secondary:hover {
  background: #8B6914;
  color: #FFF8DC;
  transform: translateY(-2px);
}

.btn-success {
  background: transparent;
  border-color: #355E3B;
  color: #355E3B;
}

.btn-success:hover {
  background: #355E3B;
  color: #FFF8DC;
  transform: translateY(-2px);
}

.btn-warning {
  background: transparent;
  border-color: #FFBF00;
  color: #FFBF00;
}

.btn-warning:hover {
  background: #FFBF00;
  color: #1A1A1A;
  transform: translateY(-2px);
}

.btn-danger {
  background: transparent;
  border-color: #800020;
  color: #800020;
}

.btn-danger:hover {
  background: #800020;
  color: #FFF8DC;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  border-color: #1A1A1A;
  color: #1A1A1A;
}

.btn-outline:hover {
  background: #1A1A1A;
  color: #FFF8DC;
  transform: translateY(-2px);
}

/* Typography Section */
.typography-section {
  margin-bottom: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 2px solid #FFD700;
}

.typography-section h2 {
  color: #1A1A1A;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.demo-h1 {
  color: #1A1A1A;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.demo-h2 {
  color: #FFD700;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.demo-h3 {
  color: #8B6914;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.demo-paragraph {
  color: #1A1A1A;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.demo-secondary-text {
  color: #355E3B;
  line-height: 1.6;
  font-style: italic;
  font-size: 1rem;
}

/* Card Examples */
.card-examples-section {
  margin-bottom: 3rem;
}

.card-examples-section h2 {
  color: #1A1A1A;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.demo-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(26, 26, 26, 0.1);
  transition: transform 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-4px);
}

.primary-card {
  border-left: 5px solid #FFD700;
}

.secondary-card {
  border-left: 5px solid #355E3B;
}

.accent-card {
  border-left: 5px solid #FFBF00;
}

.demo-card h3 {
  color: #1A1A1A;
  margin-bottom: 1rem;
}

.demo-card p {
  color: #355E3B;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.card-btn {
  padding: 0.5rem 1rem;
  border-radius: 25px;
  border: 2px solid #FFD700;
  background: transparent;
  color: #FFD700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-btn:hover {
  background: #FFD700;
  color: #1A1A1A;
}

.card-btn.secondary {
  border-color: #355E3B;
  color: #355E3B;
}

.card-btn.secondary:hover {
  background: #355E3B;
  color: #FFF8DC;
}

.card-btn.accent {
  border-color: #FFBF00;
  color: #FFBF00;
}

.card-btn.accent:hover {
  background: #FFBF00;
  color: #1A1A1A;
}

/* Cultural Significance */
.cultural-section {
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #1A1A1A 0%, #355E3B 100%);
  color: #FFF8DC;
  padding: 2rem;
  border-radius: 12px;
}

.cultural-section h2 {
  color: #FFD700;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.significance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.significance-item {
  text-align: center;
}

.significance-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1.5rem;
  font-weight: bold;
  border: 3px solid #FFD700;
}

.alpha-phi-alpha {
  background: #FFD700;
  color: #1A1A1A;
}

.pan-african {
  background: #355E3B;
  color: #FFD700;
}

.empowerment {
  background: #FFBF00;
  color: #1A1A1A;
}

.significance-item h3 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.significance-item p {
  line-height: 1.6;
  opacity: 0.9;
}

/* Accessibility Section */
.accessibility-section {
  margin-bottom: 3rem;
}

.accessibility-section h2 {
  color: #1A1A1A;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.accessibility-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.accessibility-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  border: 2px solid #355E3B;
  box-shadow: 0 4px 16px rgba(26, 26, 26, 0.1);
}

.accessibility-score {
  font-size: 3rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.accessibility-item h3 {
  color: #1A1A1A;
  margin-bottom: 1rem;
}

.accessibility-item p {
  color: #355E3B;
  line-height: 1.6;
}

/* Footer */
.demo-footer {
  text-align: center;
  padding: 2rem;
  background: #1A1A1A;
  color: #FFF8DC;
  border-radius: 12px;
  margin-top: 2rem;
}

.demo-footer p {
  margin: 0.5rem 0;
}

.demo-footer p:first-child {
  font-size: 1.2rem;
  font-weight: bold;
  color: #FFD700;
}

/* Responsive Design */
@media (max-width: 768px) {
  .color-theme-demo {
    padding: 1rem;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .color-grid,
  .button-grid,
  .card-grid,
  .significance-grid,
  .accessibility-grid {
    grid-template-columns: 1fr;
  }
}
