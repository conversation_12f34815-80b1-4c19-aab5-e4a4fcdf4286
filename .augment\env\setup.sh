#!/bin/bash

cd /mnt/persist/workspace

echo "=== Fetching from remote repository ==="
git fetch origin

echo "=== Checking available branches ==="
git branch -a

echo "=== Pulling code from master branch ==="
git pull origin master

echo "=== Checking workspace contents after pull ==="
ls -la

echo "=== Looking for project files ==="
find . -maxdepth 2 -name "package.json" -o -name "requirements.txt" -o -name "*.py" -o -name "*.js" -o -name "Dockerfile" -o -name "Makefile" | head -10

echo "=== Checking for package.json specifically ==="
if [ -f "package.json" ]; then
    echo "Found package.json:"
    cat package.json
fi