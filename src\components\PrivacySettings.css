/* Privacy Settings Styles */
.privacy-settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-md);
}

.privacy-settings-content {
  background: var(--color-light);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
}

.privacy-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  border-bottom: 2px solid var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
}

.privacy-header h2 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: 700;
}

.close-privacy-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-privacy-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Tabs */
.privacy-tabs {
  display: flex;
  background: var(--color-gray-light);
  border-bottom: 1px solid rgba(184, 134, 11, 0.2);
  overflow-x: auto;
}

.privacy-tab {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  border: none;
  background: transparent;
  color: var(--color-gray-medium);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  min-width: 120px;
}

.privacy-tab:hover {
  background: rgba(184, 134, 11, 0.1);
  color: var(--color-primary);
}

.privacy-tab.active {
  background: var(--color-light);
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  font-weight: 600;
}

/* Body */
.privacy-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.privacy-section {
  margin-bottom: var(--space-xl);
}

.privacy-section h3 {
  color: var(--color-primary);
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-xl);
  font-weight: 600;
  border-bottom: 2px solid rgba(184, 134, 11, 0.2);
  padding-bottom: var(--space-sm);
}

.setting-group {
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background: rgba(184, 134, 11, 0.05);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-primary);
}

.setting-label {
  display: block;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: var(--space-sm);
  font-size: var(--text-base);
}

.setting-select {
  width: 100%;
  padding: var(--space-sm);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background: var(--color-light);
  color: var(--color-dark);
  transition: var(--transition-normal);
}

.setting-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(184, 134, 11, 0.2);
}

.setting-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--color-dark);
  position: relative;
  padding-left: var(--space-xl);
}

.setting-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 20px;
  background-color: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.setting-checkbox:hover .checkmark {
  border-color: var(--color-primary);
}

.setting-checkbox input:checked ~ .checkmark {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  border-color: var(--color-primary);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid var(--color-light);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.setting-description {
  display: block;
  color: var(--color-gray-medium);
  font-size: var(--text-sm);
  margin-top: var(--space-xs);
  font-style: italic;
  line-height: 1.4;
}

/* Footer */
.privacy-footer {
  display: flex;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-top: 1px solid rgba(184, 134, 11, 0.2);
  background: var(--color-gray-light);
}

.save-privacy-btn {
  flex: 1;
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-normal);
}

.save-privacy-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.save-privacy-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-privacy-btn {
  background: var(--color-light);
  color: var(--color-gray-medium);
  border: 2px solid var(--color-gray-medium);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.cancel-privacy-btn:hover {
  background: var(--color-gray-medium);
  color: var(--color-light);
}

.loading-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--color-gray-medium);
  font-size: var(--text-lg);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .privacy-settings-modal {
    padding: var(--space-sm);
  }
  
  .privacy-settings-content {
    max-height: 95vh;
  }
  
  .privacy-header {
    padding: var(--space-md);
  }
  
  .privacy-header h2 {
    font-size: var(--text-xl);
  }
  
  .privacy-body {
    padding: var(--space-md);
  }
  
  .privacy-tabs {
    overflow-x: auto;
  }
  
  .privacy-tab {
    padding: var(--space-sm);
    font-size: var(--text-sm);
    min-width: 100px;
  }
  
  .setting-group {
    padding: var(--space-sm);
  }
  
  .privacy-footer {
    padding: var(--space-md);
    flex-direction: column;
  }
  
  .save-privacy-btn,
  .cancel-privacy-btn {
    padding: var(--space-sm);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .checkmark {
    border-width: 3px;
  }
  
  .setting-select {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .privacy-tab,
  .save-privacy-btn,
  .cancel-privacy-btn,
  .close-privacy-btn,
  .checkmark {
    transition: none;
  }
}
