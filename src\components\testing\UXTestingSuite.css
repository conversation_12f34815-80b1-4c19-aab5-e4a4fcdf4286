/* NAROOP UX Testing Suite - Black & Gold Theme */

.ux-testing__actions {
  display: flex;
  gap: var(--space-sm);
}

.ux-testing__run-all-btn {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  border: 2px solid var(--color-heritage-black);
  border-radius: 24px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
}

.ux-testing__run-all-btn:hover {
  background: var(--color-empowerment-amber);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

/* Test Cards */
.ux-testing__test-card {
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-forest);
  border-radius: 12px;
  padding: var(--space-lg);
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-height: 200px;
}

.ux-testing__test-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(26, 26, 26, 0.15);
  border-color: var(--color-heritage-gold);
}

.ux-testing__test-card h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0;
}

.ux-testing__test-card p {
  font-size: 0.875rem;
  color: var(--color-heritage-forest);
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.ux-testing__test-btn {
  background: transparent;
  border: 2px solid var(--color-heritage-forest);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-heritage-forest);
  min-height: 40px;
  margin-top: auto;
}

.ux-testing__test-btn:hover:not(:disabled) {
  background: var(--color-heritage-forest);
  color: var(--color-heritage-cream);
  transform: translateY(-1px);
}

.ux-testing__test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
}

.ux-testing__result {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--space-xs) var(--space-sm);
  border-radius: 12px;
  margin-top: var(--space-xs);
}

.ux-testing__result.success {
  background: var(--color-heritage-forest);
  color: var(--color-heritage-cream);
}

.ux-testing__result.error {
  background: var(--color-heritage-burgundy);
  color: var(--color-heritage-cream);
}

.ux-testing__result.warning {
  background: var(--color-empowerment-amber);
  color: var(--color-heritage-black);
}

/* Component Testing Sections */
.ux-testing__component-section {
  margin-bottom: var(--space-xl);
}

.ux-testing__component-section h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0 0 var(--space-lg) 0;
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--color-heritage-gold);
}

/* Test Results */
.ux-testing__results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-lg);
}

.ux-testing__result-section {
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-gold);
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 4px 16px rgba(26, 26, 26, 0.1);
}

.ux-testing__result-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0 0 var(--space-md) 0;
  border-bottom: 2px solid var(--color-heritage-gold);
  padding-bottom: var(--space-xs);
}

.ux-testing__issues {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
}

.ux-testing__issue {
  padding: var(--space-sm);
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.4;
  border-left: 4px solid;
}

.ux-testing__issue--success {
  background: rgba(53, 94, 59, 0.1);
  border-left-color: var(--color-heritage-forest);
  color: var(--color-heritage-forest);
}

.ux-testing__issue--info {
  background: rgba(255, 215, 0, 0.1);
  border-left-color: var(--color-heritage-gold);
  color: var(--color-heritage-black);
}

.ux-testing__issue--warning {
  background: rgba(255, 191, 0, 0.1);
  border-left-color: var(--color-empowerment-amber);
  color: var(--color-heritage-black);
}

.ux-testing__issue--error {
  background: rgba(128, 0, 32, 0.1);
  border-left-color: var(--color-heritage-burgundy);
  color: var(--color-heritage-burgundy);
}

.ux-testing__score {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-heritage-gold);
  text-align: center;
  padding: var(--space-md);
  background: linear-gradient(135deg, var(--color-heritage-black) 0%, var(--color-heritage-deep-gold) 100%);
  color: var(--color-heritage-gold);
  border-radius: 8px;
  margin-bottom: var(--space-md);
}

.ux-testing__responsive-scores {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.ux-testing__responsive-scores > div {
  background: var(--color-prosperity-champagne);
  padding: var(--space-sm);
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  color: var(--color-heritage-black);
  border: 1px solid var(--color-heritage-forest);
}

.ux-testing__performance-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-sm);
}

.ux-testing__performance-metrics > div {
  background: var(--color-prosperity-champagne);
  padding: var(--space-sm);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-heritage-black);
  border: 1px solid var(--color-heritage-forest);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .ux-testing__results {
    grid-template-columns: 1fr;
  }

  .ux-testing__responsive-scores {
    grid-template-columns: 1fr;
  }

  .ux-testing__performance-metrics {
    grid-template-columns: 1fr;
  }

  .ux-testing__test-card {
    padding: var(--space-md);
    min-height: 180px;
  }

  .ux-testing__component-section h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .ux-testing__actions {
    width: 100%;
  }

  .ux-testing__run-all-btn {
    width: 100%;
    justify-content: center;
  }

  .ux-testing__test-card {
    padding: var(--space-sm);
    min-height: 160px;
  }

  .ux-testing__result-section {
    padding: var(--space-md);
  }
}

/* Animation for loading states */
.ux-testing__loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--color-heritage-forest);
  border-radius: 50%;
  border-top-color: var(--color-heritage-gold);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Focus States for Accessibility */
.ux-testing__test-btn:focus,
.ux-testing__run-all-btn:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .ux-testing__test-card,
  .ux-testing__result-section {
    border-width: 3px;
  }

  .ux-testing__issue {
    border-left-width: 6px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .ux-testing__test-card:hover,
  .ux-testing__test-btn:hover,
  .ux-testing__run-all-btn:hover {
    transform: none;
  }

  .ux-testing__test-btn:disabled {
    animation: none;
  }

  .ux-testing__loading {
    animation: none;
    border-top-color: var(--color-heritage-forest);
  }
}
