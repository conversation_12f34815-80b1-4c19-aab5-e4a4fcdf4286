import React, { useEffect, useState } from 'react';
import { 
  testNAROOPAccessibility, 
  generateAccessibilityReport, 
  NAROOP_COLORS,
  simulateColorBlindness 
} from '../utils/colorAccessibility';

const AccessibilityTest = () => {
  const [results, setResults] = useState(null);
  const [colorBlindnessType, setColorBlindnessType] = useState('normal');

  useEffect(() => {
    const testResults = testNAROOPAccessibility();
    setResults(testResults);
    
    // Log detailed report to console
    console.log(generateAccessibilityReport());
  }, []);

  const getStatusIcon = (passes) => passes ? '✅' : '❌';
  const getStatusColor = (passes) => passes ? '#355E3B' : '#800020';

  const renderColorSwatch = (color, label) => {
    const displayColor = colorBlindnessType === 'normal' 
      ? color 
      : simulateColorBlindness(color, colorBlindnessType);
      
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        margin: '8px 0',
        padding: '8px',
        backgroundColor: '#FFF8DC',
        borderRadius: '8px',
        border: '1px solid #B87333'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          backgroundColor: displayColor,
          border: '2px solid #1A1A1A',
          borderRadius: '8px',
          marginRight: '12px'
        }}></div>
        <div>
          <div style={{ fontWeight: 'bold', color: '#1A1A1A' }}>{label}</div>
          <div style={{ fontSize: '12px', color: '#B87333', fontFamily: 'monospace' }}>
            {displayColor}
          </div>
        </div>
      </div>
    );
  };

  if (!results) {
    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: '#FFF8DC',
        color: '#1A1A1A',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <h2>Loading Accessibility Tests...</h2>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#FFF8DC',
      color: '#1A1A1A',
      fontFamily: 'system-ui, sans-serif',
      minHeight: '100vh'
    }}>
      <h1 style={{ 
        color: '#1A1A1A', 
        borderBottom: '3px solid #FFD700',
        paddingBottom: '10px'
      }}>
        NAROOP Black & Gold Accessibility Report
      </h1>

      {/* Color Blindness Simulation */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #B87333'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>Color Blindness Simulation</h3>
        <select 
          value={colorBlindnessType}
          onChange={(e) => setColorBlindnessType(e.target.value)}
          style={{
            padding: '8px 12px',
            borderRadius: '25px',
            border: '2px solid #FFD700',
            backgroundColor: 'transparent',
            color: '#1A1A1A',
            fontSize: '14px'
          }}
        >
          <option value="normal">Normal Vision</option>
          <option value="protanopia">Protanopia (Red-blind)</option>
          <option value="deuteranopia">Deuteranopia (Green-blind)</option>
          <option value="tritanopia">Tritanopia (Blue-blind)</option>
        </select>
      </div>

      {/* Color Palette Display */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #B87333'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>NAROOP Color Palette</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
          {Object.entries(NAROOP_COLORS).map(([key, color]) => 
            renderColorSwatch(color, key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()))
          )}
        </div>
      </div>

      {/* Summary */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #FFD700'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>Test Summary</h3>
        <div style={{ fontSize: '18px', marginBottom: '16px' }}>
          <div>Total Tests: <strong>{results.summary.totalTests}</strong></div>
          <div style={{ color: '#355E3B' }}>Passed: <strong>{results.summary.passed}</strong></div>
          <div style={{ color: '#800020' }}>Failed: <strong>{results.summary.failed}</strong></div>
          <div>Success Rate: <strong>{Math.round((results.summary.passed / results.summary.totalTests) * 100)}%</strong></div>
        </div>
      </div>

      {/* Text Combinations */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #B87333'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>Text Combinations (WCAG AA)</h3>
        {results.textCombinations.map((test, index) => (
          <div key={index} style={{ 
            padding: '12px',
            margin: '8px 0',
            backgroundColor: '#F7E7CE',
            borderRadius: '8px',
            border: `2px solid ${getStatusColor(test.passes)}`
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <span style={{ fontWeight: 'bold' }}>
                {getStatusIcon(test.passes)} {test.name}
              </span>
              <span style={{ 
                fontFamily: 'monospace',
                backgroundColor: test.passes ? '#355E3B' : '#800020',
                color: '#FFF8DC',
                padding: '4px 8px',
                borderRadius: '12px',
                fontSize: '12px'
              }}>
                {test.ratio}:1 (req: {test.requiredRatio}:1)
              </span>
            </div>
            <div style={{ 
              padding: '8px 12px',
              backgroundColor: test.bg,
              color: test.fg,
              borderRadius: '4px',
              border: '1px solid #B87333'
            }}>
              Sample text in this color combination
            </div>
          </div>
        ))}
      </div>

      {/* Button Combinations */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #B87333'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>Button Combinations (WCAG AA)</h3>
        {results.buttonCombinations.map((test, index) => (
          <div key={index} style={{ 
            padding: '12px',
            margin: '8px 0',
            backgroundColor: '#F7E7CE',
            borderRadius: '8px',
            border: `2px solid ${getStatusColor(test.passes)}`
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <span style={{ fontWeight: 'bold' }}>
                {getStatusIcon(test.passes)} {test.name}
              </span>
              <span style={{ 
                fontFamily: 'monospace',
                backgroundColor: test.passes ? '#355E3B' : '#800020',
                color: '#FFF8DC',
                padding: '4px 8px',
                borderRadius: '12px',
                fontSize: '12px'
              }}>
                {test.ratio}:1 (req: {test.requiredRatio}:1)
              </span>
            </div>
            <button style={{ 
              padding: '8px 16px',
              backgroundColor: test.bg,
              color: test.fg,
              border: test.border ? `2px solid ${test.border}` : 'none',
              borderRadius: '25px',
              cursor: 'pointer',
              fontSize: '14px'
            }}>
              Sample Button
            </button>
          </div>
        ))}
      </div>

      {/* Cultural Significance */}
      <div style={{ 
        backgroundColor: '#FFFFFF',
        padding: '20px',
        borderRadius: '12px',
        margin: '20px 0',
        border: '2px solid #FFD700'
      }}>
        <h3 style={{ color: '#1A1A1A', marginTop: 0 }}>Cultural Significance</h3>
        <div style={{ lineHeight: '1.6', color: '#1A1A1A' }}>
          <p><strong>Black & Gold Heritage:</strong> These colors honor the tradition of Alpha Phi Alpha Fraternity (founded 1906), the first African American intercollegiate fraternity, and represent excellence, scholarship, and brotherhood in the Black community.</p>
          <p><strong>Symbolism:</strong> Black represents strength, elegance, and the rich heritage of African ancestry. Gold represents prosperity, achievement, excellence, and the bright future of the community.</p>
          <p><strong>Modern Relevance:</strong> This palette embodies the journey from struggle to triumph, representing resilience, success, and the empowerment of the African American community.</p>
        </div>
      </div>
    </div>
  );
};

export default AccessibilityTest;
