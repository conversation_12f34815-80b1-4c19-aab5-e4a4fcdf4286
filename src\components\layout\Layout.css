/* NAROOP Layout System - Black & Gold Theme */

/* Base Layout */
.naroop-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--gradient-community);
  color: var(--color-heritage-black);
}

.naroop-layout__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-lg) 0;
}

.naroop-layout__container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Container Sizes */
.naroop-layout--narrow .naroop-layout__container {
  max-width: 768px;
}

.naroop-layout--default .naroop-layout__container {
  max-width: 1200px;
}

.naroop-layout--wide .naroop-layout__container {
  max-width: 1440px;
}

.naroop-layout--full .naroop-layout__container {
  max-width: none;
  padding: 0;
}

/* Page Layout */
.page-layout__header {
  margin-bottom: var(--space-xl);
}

.page-layout__breadcrumbs {
  margin-bottom: var(--space-md);
}

.page-layout__breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: var(--space-xs);
}

.page-layout__breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.page-layout__breadcrumb-link {
  color: var(--color-heritage-forest);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.page-layout__breadcrumb-link:hover {
  color: var(--color-heritage-gold);
}

.page-layout__breadcrumb-current {
  color: var(--color-heritage-black);
  font-size: 0.875rem;
  font-weight: 600;
}

.page-layout__breadcrumb-separator {
  width: 16px;
  height: 16px;
  color: var(--color-heritage-forest);
  opacity: 0.6;
}

.page-layout__title-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--space-lg);
}

.page-layout__title-content {
  flex: 1;
  min-width: 0;
}

.page-layout__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0 0 var(--space-sm) 0;
  line-height: 1.2;
}

.page-layout__subtitle {
  font-size: 1.125rem;
  color: var(--color-heritage-forest);
  margin: 0;
  line-height: 1.5;
}

.page-layout__actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-shrink: 0;
}

.page-layout__content {
  display: flex;
  gap: var(--space-xl);
  align-items: flex-start;
}

.page-layout__content--with-sidebar {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--space-xl);
}

.page-layout__main-content {
  flex: 1;
  min-width: 0;
}

.page-layout__sidebar {
  flex-shrink: 0;
  width: 300px;
}

/* Grid Layout */
.grid-layout {
  display: grid;
  width: 100%;
}

.grid-layout--columns-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-layout--columns-1 {
  grid-template-columns: 1fr;
}

.grid-layout--columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-layout--columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-layout--columns-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-layout--gap-small {
  gap: var(--space-md);
}

.grid-layout--gap-medium {
  gap: var(--space-lg);
}

.grid-layout--gap-large {
  gap: var(--space-xl);
}

/* Section Layout */
.section-layout {
  margin-bottom: var(--space-2xl);
}

.section-layout--featured {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-empowerment-amber) 100%);
  color: var(--color-heritage-black);
  padding: var(--space-xl);
  border-radius: 16px;
  border: 2px solid var(--color-heritage-black);
  box-shadow: 0 8px 32px rgba(26, 26, 26, 0.15);
}

.section-layout--community {
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-forest);
  border-radius: 16px;
  padding: var(--space-xl);
  box-shadow: 0 4px 16px rgba(26, 26, 26, 0.1);
}

.section-layout__header {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.section-layout__header-content {
  flex: 1;
  min-width: 0;
}

.section-layout__title {
  font-size: 2rem;
  font-weight: 700;
  color: inherit;
  margin: 0 0 var(--space-xs) 0;
  line-height: 1.3;
}

.section-layout__subtitle {
  font-size: 1rem;
  color: inherit;
  opacity: 0.8;
  margin: 0;
  line-height: 1.5;
}

.section-layout__actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-shrink: 0;
}

.section-layout__content {
  width: 100%;
}

/* Content Layout */
.content-layout {
  width: 100%;
  margin: 0 auto;
}

.content-layout--narrow {
  max-width: 600px;
}

.content-layout--readable {
  max-width: 768px;
}

.content-layout--wide {
  max-width: 1000px;
}

.content-layout h1,
.content-layout h2,
.content-layout h3,
.content-layout h4,
.content-layout h5,
.content-layout h6 {
  color: var(--color-heritage-black);
  line-height: 1.3;
  margin-top: var(--space-lg);
  margin-bottom: var(--space-md);
}

.content-layout h1:first-child,
.content-layout h2:first-child,
.content-layout h3:first-child {
  margin-top: 0;
}

.content-layout p {
  line-height: 1.6;
  margin-bottom: var(--space-md);
  color: var(--color-heritage-black);
}

.content-layout ul,
.content-layout ol {
  margin-bottom: var(--space-md);
  padding-left: var(--space-lg);
}

.content-layout li {
  margin-bottom: var(--space-xs);
  line-height: 1.6;
  color: var(--color-heritage-black);
}

.content-layout blockquote {
  border-left: 4px solid var(--color-heritage-gold);
  padding-left: var(--space-md);
  margin: var(--space-lg) 0;
  font-style: italic;
  color: var(--color-heritage-forest);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .page-layout__content--with-sidebar {
    grid-template-columns: 1fr;
  }

  .page-layout__sidebar {
    width: 100%;
    order: -1;
  }

  .grid-layout--columns-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-layout--columns-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .naroop-layout__container {
    padding: 0 var(--space-sm);
  }

  .naroop-layout__main {
    padding: var(--space-md) 0;
  }

  .page-layout__title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .page-layout__title {
    font-size: 2rem;
  }

  .page-layout__actions {
    width: 100%;
    justify-content: flex-start;
  }

  .page-layout__content {
    gap: var(--space-lg);
  }

  .section-layout__header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .section-layout__title {
    font-size: 1.5rem;
  }

  .section-layout--featured,
  .section-layout--community {
    padding: var(--space-lg);
  }

  .grid-layout--columns-auto {
    grid-template-columns: 1fr;
  }

  .grid-layout--columns-2,
  .grid-layout--columns-3,
  .grid-layout--columns-4 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .page-layout__title {
    font-size: 1.75rem;
  }

  .section-layout__title {
    font-size: 1.25rem;
  }

  .page-layout__breadcrumb-list {
    flex-wrap: wrap;
  }

  .section-layout--featured,
  .section-layout--community {
    padding: var(--space-md);
  }
}

/* Focus States for Accessibility */
.page-layout__breadcrumb-link:focus {
  outline: 2px solid var(--color-heritage-gold);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Print Styles */
@media print {
  .naroop-layout {
    background: white;
    color: black;
  }

  .section-layout--featured,
  .section-layout--community {
    background: white;
    border: 1px solid black;
    box-shadow: none;
  }
}
