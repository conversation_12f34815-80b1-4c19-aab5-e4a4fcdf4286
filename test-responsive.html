<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NAROOP Responsive Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #591C28;
            margin-top: 0;
        }
        .breakpoint-info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #6E8C65;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        .current-size {
            font-size: 18px;
            font-weight: bold;
            color: #591C28;
            text-align: center;
            padding: 10px;
            background: #FDFBF5;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 NAROOP Responsive Navigation Test</h1>
        
        <div class="current-size" id="currentSize">
            Current Screen Size: <span id="sizeDisplay"></span>
        </div>

        <div class="test-section">
            <h2>📱 Mobile Navigation Test (320px - 767px)</h2>
            <div class="breakpoint-info">
                <strong>Expected Behavior:</strong>
                <ul>
                    <li>✅ Hamburger menu button should be VISIBLE</li>
                    <li>✅ Full navigation menu should be HIDDEN by default</li>
                    <li>✅ Navigation menu should only show when hamburger is clicked</li>
                    <li>✅ Navigation should stack vertically when open</li>
                </ul>
            </div>
            <div class="test-instructions">
                <strong>Test Instructions:</strong>
                <ol>
                    <li>Resize your browser window to mobile size (320px - 767px)</li>
                    <li>Go to <a href="http://localhost:3000" target="_blank">NAROOP App</a></li>
                    <li>Log in to access the navigation</li>
                    <li>Verify hamburger menu is visible and navigation is hidden</li>
                    <li>Click hamburger menu to test toggle functionality</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>💻 Desktop/Tablet Navigation Test (768px+)</h2>
            <div class="breakpoint-info">
                <strong>Expected Behavior:</strong>
                <ul>
                    <li>✅ Hamburger menu button should be HIDDEN</li>
                    <li>✅ Full navigation menu should be VISIBLE</li>
                    <li>✅ Navigation should display horizontally</li>
                    <li>✅ No toggle functionality needed</li>
                </ul>
            </div>
            <div class="test-instructions">
                <strong>Test Instructions:</strong>
                <ol>
                    <li>Resize your browser window to desktop size (768px+)</li>
                    <li>Go to <a href="http://localhost:3000" target="_blank">NAROOP App</a></li>
                    <li>Log in to access the navigation</li>
                    <li>Verify hamburger menu is hidden and navigation is visible</li>
                    <li>Verify navigation displays horizontally</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>✍️ Story Input Functionality Test</h2>
            <div class="breakpoint-info">
                <strong>Expected Behavior:</strong>
                <ul>
                    <li>✅ Story title input should accept and display text</li>
                    <li>✅ Story content textarea should accept and display text</li>
                    <li>✅ Character counter should update as you type</li>
                    <li>✅ Form validation should work properly</li>
                    <li>✅ Visual feedback should show for valid/invalid inputs</li>
                </ul>
            </div>
            <div class="test-instructions">
                <strong>Test Instructions:</strong>
                <ol>
                    <li>Go to <a href="http://localhost:3000" target="_blank">NAROOP App</a></li>
                    <li>Log in and navigate to Stories section</li>
                    <li>Try typing in the "Story Title" field</li>
                    <li>Try typing in the "Your Story" textarea</li>
                    <li>Verify text appears as you type</li>
                    <li>Check that character counter updates</li>
                    <li>Test form validation by submitting empty/invalid data</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="testResults">
                <p>Complete the tests above and document your results here:</p>
                <div class="status" id="mobileNavResult">
                    <strong>Mobile Navigation:</strong> ⏳ Pending Test
                </div>
                <div class="status" id="desktopNavResult">
                    <strong>Desktop Navigation:</strong> ⏳ Pending Test
                </div>
                <div class="status" id="storyInputResult">
                    <strong>Story Input:</strong> ⏳ Pending Test
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const sizeDisplay = document.getElementById('sizeDisplay');
            
            let category = '';
            if (width <= 767) {
                category = 'Mobile';
            } else if (width <= 1024) {
                category = 'Tablet';
            } else {
                category = 'Desktop';
            }
            
            sizeDisplay.textContent = `${width}px × ${height}px (${category})`;
        }

        // Update size on load and resize
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);

        // Test result functions
        function markTestResult(testId, passed, message) {
            const element = document.getElementById(testId);
            element.className = `status ${passed ? 'pass' : 'fail'}`;
            element.innerHTML = `<strong>${element.querySelector('strong').textContent}</strong> ${passed ? '✅' : '❌'} ${message}`;
        }

        // Example usage (you can call these from browser console after testing):
        // markTestResult('mobileNavResult', true, 'Hamburger menu works correctly');
        // markTestResult('desktopNavResult', true, 'Full navigation displays properly');
        // markTestResult('storyInputResult', true, 'Text input and display working');
    </script>
</body>
</html>
