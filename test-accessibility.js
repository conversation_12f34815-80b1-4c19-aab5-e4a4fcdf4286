// Simple Node.js script to test NAROOP color accessibility
// Run with: node test-accessibility.js

// NAROOP Black & Gold Color Palette
const NAROOP_COLORS = {
  // Core Foundation Colors
  heritageBlack: '#1A1A1A',
  heritageGold: '#FFD700',
  heritageDeepGold: '#8B6914',
  heritageCream: '#FFF8DC',
  
  // Empowerment Colors
  empowermentAmber: '#FFBF00',
  communityBronze: '#CD7F32',
  wisdomCopper: '#8B4513',
  prosperityChampagne: '#F7E7CE',
  
  // Cultural Heritage Accents
  heritageForest: '#355E3B',
  heritageBurgundy: '#800020',
  heritageRoyal: '#4B0082',
  heritageEarth: '#8B4513',
  
  // Supporting Colors
  white: '#FFFFFF',
  lightGray: '#F5F5F5'
};

// Convert hex color to RGB values
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// Calculate relative luminance of a color
function getRelativeLuminance(rgb) {
  const { r, g, b } = rgb;
  
  // Convert to sRGB
  const rsRGB = r / 255;
  const gsRGB = g / 255;
  const bsRGB = b / 255;
  
  // Apply gamma correction
  const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  
  // Calculate relative luminance
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
}

// Calculate contrast ratio between two colors
function getContrastRatio(color1, color2) {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getRelativeLuminance(rgb1);
  const lum2 = getRelativeLuminance(rgb2);
  
  const lighter = Math.max(lum1, lum2);
  const darker = Math.min(lum1, lum2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

// Check if color combination meets WCAG AA standards
function checkWCAGCompliance(foreground, background, level = 'AA', size = 'normal') {
  const ratio = getContrastRatio(foreground, background);
  
  let requiredRatio;
  if (level === 'AAA') {
    requiredRatio = size === 'large' ? 4.5 : 7;
  } else { // AA
    requiredRatio = size === 'large' ? 3 : 4.5;
  }
  
  return {
    ratio: Math.round(ratio * 100) / 100,
    requiredRatio,
    passes: ratio >= requiredRatio,
    level,
    size
  };
}

// Test all NAROOP color combinations for accessibility
function testNAROOPAccessibility() {
  console.log('\n=== NAROOP Black & Gold Accessibility Report ===\n');
  
  // Test primary text combinations
  const textTests = [
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.heritageCream, name: 'Black text on cream' },
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.white, name: 'Black text on white' },
    { fg: NAROOP_COLORS.heritageCream, bg: NAROOP_COLORS.heritageBlack, name: 'Cream text on black' },
    { fg: NAROOP_COLORS.heritageGold, bg: NAROOP_COLORS.heritageBlack, name: 'Gold text on black' },
    { fg: NAROOP_COLORS.wisdomCopper, bg: NAROOP_COLORS.heritageCream, name: 'Copper text on cream' },
    { fg: NAROOP_COLORS.heritageForest, bg: NAROOP_COLORS.heritageCream, name: 'Forest text on cream' },
    { fg: NAROOP_COLORS.heritageBurgundy, bg: NAROOP_COLORS.heritageCream, name: 'Burgundy text on cream' },
    { fg: NAROOP_COLORS.heritageDeepGold, bg: NAROOP_COLORS.heritageCream, name: 'Deep gold text on cream' }
  ];
  
  console.log('TEXT COMBINATIONS (WCAG AA):');
  console.log('============================');
  
  let totalTests = 0;
  let passedTests = 0;
  
  textTests.forEach(test => {
    const result = checkWCAGCompliance(test.fg, test.bg, 'AA', 'normal');
    const status = result.passes ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}: ${result.ratio}:1 (required: ${result.requiredRatio}:1)`);
    
    totalTests++;
    if (result.passes) passedTests++;
  });
  
  // Test button combinations
  const buttonTests = [
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.heritageGold, name: 'Gold button filled (black text)' },
    { fg: NAROOP_COLORS.heritageCream, bg: NAROOP_COLORS.heritageDeepGold, name: 'Deep gold button filled (cream text)' },
    { fg: NAROOP_COLORS.heritageCream, bg: NAROOP_COLORS.heritageBurgundy, name: 'Burgundy button filled (cream text)' },
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.empowermentAmber, name: 'Amber button filled (black text)' }
  ];
  
  console.log('\nBUTTON COMBINATIONS (WCAG AA):');
  console.log('===============================');
  
  buttonTests.forEach(test => {
    const result = checkWCAGCompliance(test.fg, test.bg, 'AA', 'normal');
    const status = result.passes ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}: ${result.ratio}:1 (required: ${result.requiredRatio}:1)`);
    
    totalTests++;
    if (result.passes) passedTests++;
  });
  
  console.log('\nSUMMARY:');
  console.log('========');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  console.log('\nCULTURAL SIGNIFICANCE:');
  console.log('=====================');
  console.log('Black & Gold Heritage: These colors honor Alpha Phi Alpha Fraternity (1906),');
  console.log('the first African American intercollegiate fraternity, representing excellence,');
  console.log('scholarship, and brotherhood in the Black community.');
  console.log('');
  console.log('Symbolism: Black represents strength and heritage; Gold represents prosperity');
  console.log('and achievement. Together they embody the journey from struggle to triumph.');
  
  return { totalTests, passedTests, failedTests: totalTests - passedTests };
}

// Run the tests
testNAROOPAccessibility();
