/**
 * Deployment environment check utility
 * Helps debug common deployment issues
 */

export const checkDeploymentEnvironment = () => {
  console.log('🔍 NAROOP Deployment Check');
  console.log('Environment:', import.meta.env.MODE);
  console.log('Base URL:', import.meta.env.BASE_URL);
  console.log('Production:', import.meta.env.PROD);
  console.log('Development:', import.meta.env.DEV);
  
  // Check if Firebase config is available
  try {
    const firebaseConfig = {
      apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
      authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
      projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
      storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
      appId: import.meta.env.VITE_FIREBASE_APP_ID
    };
    
    const hasAllKeys = Object.values(firebaseConfig).every(value => value && value !== 'undefined');
    
    if (hasAllKeys) {
      console.log('✅ Firebase configuration found');
    } else {
      console.error('❌ Missing Firebase environment variables');
      console.log('Available keys:', Object.keys(firebaseConfig).filter(key => firebaseConfig[key] && firebaseConfig[key] !== 'undefined'));
      console.log('Missing keys:', Object.keys(firebaseConfig).filter(key => !firebaseConfig[key] || firebaseConfig[key] === 'undefined'));
    }
    
    return hasAllKeys;
  } catch (error) {
    console.error('❌ Error checking Firebase config:', error);
    return false;
  }
};

export const logDeploymentInfo = () => {
  console.log('📱 NAROOP v1.2.1 - Deployment Info');
  console.log('User Agent:', navigator.userAgent);
  console.log('URL:', window.location.href);
  console.log('Protocol:', window.location.protocol);
  console.log('Host:', window.location.host);
  console.log('Pathname:', window.location.pathname);
  
  // Check for common deployment issues
  if (window.location.protocol === 'file:') {
    console.warn('⚠️ Running from file:// protocol - this may cause issues');
  }
  
  if (!window.location.host.includes('localhost') && window.location.protocol === 'http:') {
    console.warn('⚠️ Running on HTTP in production - HTTPS recommended');
  }
  
  // Check if service worker is available
  if ('serviceWorker' in navigator) {
    console.log('✅ Service Worker support available');
  } else {
    console.log('ℹ️ Service Worker not supported');
  }
};

// Run checks automatically in development
if (import.meta.env.DEV) {
  logDeploymentInfo();
  checkDeploymentEnvironment();
}
